# 设置默认行为，如果 Git 认为文件是文本，则自动规范化行尾符
* text=auto

# 明确指定文本文件使用 LF 行尾符
*.js text eol=lf
*.ts text eol=lf
*.vue text eol=lf
*.json text eol=lf
*.md text eol=lf
*.yml text eol=lf
*.yaml text eol=lf
*.css text eol=lf
*.scss text eol=lf
*.html text eol=lf
*.xml text eol=lf
*.txt text eol=lf
*.sh text eol=lf

# 配置文件
*.config.js text eol=lf
*.config.ts text eol=lf
.eslintrc* text eol=lf
.prettierrc* text eol=lf
.gitignore text eol=lf
.gitattributes text eol=lf

# Husky 钩子文件
.husky/* text eol=lf

# 包管理文件
package.json text eol=lf
package-lock.json text eol=lf
pnpm-lock.yaml text eol=lf
yarn.lock text eol=lf

# 二进制文件
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.svg binary
*.woff binary
*.woff2 binary
*.ttf binary
*.eot binary
*.pdf binary
*.zip binary
*.tar.gz binary
*.exe binary
*.dll binary
*.so binary
*.dylib binary

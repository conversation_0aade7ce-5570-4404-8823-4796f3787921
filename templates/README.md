# Mattverse 项目模板系统

快速创建新应用和包的模板系统。

## 使用方法

### 创建新应用（Electron 应用）

```bash
# 交互式创建（推荐）
node scripts/generators/create-app.js

# 命令行创建
node scripts/generators/create-app.js my-new-app "我的新应用"

# 通过交互式启动器
node scripts/utils/interactive-launcher.js
# 选择 "代码生成器" -> "创建新应用"

# 通过开发管理器
node scripts/dev/dev-manager.js
# 输入命令: create-app

# 应用将创建在 apps/my-new-app/ 目录下
```

### 创建新包（可复用库）

```bash
# 交互式创建（推荐）
node scripts/generators/create-package.js

# 命令行创建
node scripts/generators/create-package.js my-utils "我的工具包"

# 通过交互式启动器
node scripts/utils/interactive-launcher.js
# 选择 "代码生成器" -> "创建新包"

# 通过开发管理器
node scripts/dev/dev-manager.js
# 输入命令: create-package

# 包将创建在 packages/my-utils/ 目录下
```

## 模板说明

### 应用模板 (app-template)
- 完整的 Electron + Vue 3 桌面应用结构
- 包含主进程、预加载脚本、渲染进程
- 预配置 TypeScript、Tailwind CSS、Vue Router、Pinia
- 包含基础页面和路由配置

### 包模板 (package-template)
- 通用的 TypeScript 包结构
- 使用 tsup 构建工具
- 预配置 ESLint、TypeScript
- 包含基础的导出结构

## 目录结构

```
templates/
├── app-template/          # 应用模板文件
├── package-template/      # 包模板文件
└── README.md             # 说明文档

scripts/
├── generators/            # 代码生成器
│   ├── create-app.js     # 创建应用脚本
│   └── create-package.js # 创建包脚本
├── utils/
│   └── interactive-launcher.js  # 交互式启动器（包含生成器）
└── dev/
    └── dev-manager.js    # 开发管理器（包含生成器命令）
```

## 创建后的操作

### 应用创建后
```bash
cd apps/your-app-name
pnpm install
pnpm dev
```

### 包创建后
```bash
cd packages/your-package-name
pnpm install
pnpm build
```

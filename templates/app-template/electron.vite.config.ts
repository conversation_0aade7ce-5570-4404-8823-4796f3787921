import { defineConfig } from 'electron-vite'
import { resolve } from 'path'
import vue from '@vitejs/plugin-vue'
import tailwindcss from '@tailwindcss/vite'
import { createAutoImportPlugins } from '../../packages/configs/src/vite/auto-import'

export default defineConfig({
  main: {
    build: {
      rollupOptions: {
        external: ['@mattverse/electron-core'],
      },
    },
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src/main'),
        '@core': resolve(__dirname, '../../packages/electron-core/src'),
      },
    },
  },
  preload: {
    build: {
      rollupOptions: {
        external: ['@mattverse/electron-core'],
      },
    },
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src/preload'),
        '@core': resolve(__dirname, '../../packages/electron-core/src'),
      },
    },
  },
  renderer: {
    plugins: [vue(), tailwindcss(), ...createAutoImportPlugins({ appName: '{{APP_NAME}}' })],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src/renderer/src'),
        '@ui': resolve(__dirname, '../../packages/mattverse-ui/src'),
        '@flow': resolve(__dirname, '../../packages/mattverse-flow/src'),
        '@configs': resolve(__dirname, '../../packages/configs/src'),
      },
    },
    assetsInclude: ['**/*.ttf', '**/*.woff', '**/*.woff2'],
  },
})

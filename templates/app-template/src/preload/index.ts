import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'

/**
 * {{APP_NAME}} 预加载脚本
 */

// 创建简单的 API
const api = {
  // 基础 API
  getConfig: () => ipcRenderer.invoke('{{APP_NAME}}:get-config'),

  // 系统 API
  platform: process.platform,
  versions: process.versions,
}

// 创建日志 API
const logger = {
  info: (message: string, ...args: any[]) => console.log(`[{{APP_NAME}}]`, message, ...args),
  warn: (message: string, ...args: any[]) => console.warn(`[{{APP_NAME}}]`, message, ...args),
  error: (message: string, ...args: any[]) => console.error(`[{{APP_NAME}}]`, message, ...args),
}

// 使用 contextBridge 暴露 API 到渲染进程
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
    contextBridge.exposeInMainWorld('logger', logger)
  } catch (error) {
    console.error('预加载脚本错误:', error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI
  // @ts-ignore (define in dts)
  window.api = api
  // @ts-ignore (define in dts)
  window.logger = logger
}

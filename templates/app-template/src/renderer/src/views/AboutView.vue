<template>
  <div class="flex h-full flex-col items-center justify-center p-8">
    <div class="absolute left-4 top-4">
      <button
        @click="$router.back()"
        class="rounded-md border px-3 py-1 text-sm hover:bg-accent"
      >
        ← 返回
      </button>
    </div>

    <div class="max-w-2xl text-center">
      <h1 class="mb-6 text-3xl font-bold">关于 {{APP_NAME}}</h1>
      
      <div class="mb-8 space-y-4 text-left">
        <div class="rounded-lg border bg-card p-4">
          <h3 class="mb-2 font-semibold">版本信息</h3>
          <p class="text-sm text-muted-foreground">版本: 1.0.0</p>
          <p class="text-sm text-muted-foreground">作者: mattverse.com</p>
        </div>

        <div class="rounded-lg border bg-card p-4">
          <h3 class="mb-2 font-semibold">技术栈</h3>
          <div class="flex flex-wrap gap-2">
            <span class="rounded bg-primary/10 px-2 py-1 text-xs">Electron</span>
            <span class="rounded bg-primary/10 px-2 py-1 text-xs">Vue 3</span>
            <span class="rounded bg-primary/10 px-2 py-1 text-xs">TypeScript</span>
            <span class="rounded bg-primary/10 px-2 py-1 text-xs">Tailwind CSS</span>
            <span class="rounded bg-primary/10 px-2 py-1 text-xs">Pinia</span>
          </div>
        </div>

        <div class="rounded-lg border bg-card p-4">
          <h3 class="mb-2 font-semibold">描述</h3>
          <p class="text-sm text-muted-foreground">{{DESCRIPTION}}</p>
        </div>
      </div>

      <div class="flex justify-center gap-4">
        <button
          @click="openHomepage"
          class="rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90"
        >
          访问主页
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * {{APP_NAME}} 关于页面
 */

const openHomepage = () => {
  window.electron?.shell.openExternal('https://mattverse.com')
}
</script>

<template>
  <div class="flex h-full flex-col items-center justify-center p-8">
    <div class="mb-8 text-center">
      <h1 class="mb-4 text-4xl font-bold text-primary">
        {{APP_NAME}}
      </h1>
      <p class="text-lg text-muted-foreground">
        {{DESCRIPTION}}
      </p>
    </div>

    <div class="grid max-w-4xl grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
      <div class="rounded-lg border bg-card p-6 shadow-sm">
        <h3 class="mb-2 text-xl font-semibold">快速开始</h3>
        <p class="text-sm text-muted-foreground">
          开始使用 {{APP_NAME}} 的基本功能
        </p>
      </div>

      <div class="rounded-lg border bg-card p-6 shadow-sm">
        <h3 class="mb-2 text-xl font-semibold">文档</h3>
        <p class="text-sm text-muted-foreground">
          查看详细的使用文档和 API 参考
        </p>
      </div>

      <div class="rounded-lg border bg-card p-6 shadow-sm">
        <h3 class="mb-2 text-xl font-semibold">设置</h3>
        <p class="text-sm text-muted-foreground">
          配置应用程序的各项设置
        </p>
      </div>
    </div>

    <div class="mt-8 flex gap-4">
      <button
        @click="$router.push('/about')"
        class="rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90"
      >
        关于应用
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * {{APP_NAME}} 首页视图
 */
</script>

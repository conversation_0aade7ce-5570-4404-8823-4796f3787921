import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate'
import { createRouter, createWebHashHistory } from 'vue-router'
import App from './App.vue'
import { routes } from './router'

// 导入样式
import '@mattverse/mattverse-ui/style.css'
import './assets/styles/index.css'

/**
 * {{APP_NAME}} 渲染进程入口
 */

// 创建 Vue 应用实例
const app = createApp(App)

// 创建 Pinia 状态管理
const pinia = createPinia()

// 配置持久化插件
pinia.use(
  createPersistedState({
    storage: localStorage,
    key: (id) => `{{APP_NAME}}_${id}`,
  })
)

// 创建路由
const router = createRouter({
  history: createWebHashHistory(),
  routes,
})

// 注册插件
app.use(pinia)
app.use(router)

// 挂载应用
app.mount('#app')

// 开发环境下的调试信息
if (import.meta.env.DEV) {
  window.logger?.info('{{APP_NAME}} 应用已启动')
}

{"name": "{{APP_NAME}}", "version": "1.0.0", "private": true, "description": "{{DESCRIPTION}}", "author": "mattverse.com", "homepage": "https://mattverse.com", "main": "out/main/index.js", "scripts": {"dev": "electron-vite dev", "build": "electron-vite build", "preview": "electron-vite preview", "build:win": "pnpm run build && electron-builder --win", "build:mac": "pnpm run build && electron-builder --mac", "build:linux": "pnpm run build && electron-builder --linux", "clean": "rimraf dist release", "lint": "eslint src --ext .ts,.tsx,.vue", "lint:fix": "eslint src --ext .ts,.tsx,.vue --fix", "typecheck": "vue-tsc --noEmit"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^3.0.0", "@mattverse/electron-core": "workspace:*", "@mattverse/mattverse-flow": "workspace:*", "@mattverse/mattverse-ui": "workspace:*", "@mattverse/shared": "workspace:*", "@vue-flow/background": "^1.3.0", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.45.0", "@vue-flow/minimap": "^1.5.0", "@vueuse/core": "^11.0.0", "axios": "^1.7.0", "dayjs": "^1.11.0", "dexie": "^4.0.0", "electron-log": "^5.2.0", "lucide-vue-next": "^0.447.0", "nanoid": "^5.0.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.1.0", "radix-vue": "^1.9.0", "vee-validate": "^4.13.0", "vue": "^3.5.0", "vue-router": "^4.4.0", "vue-sonner": "^1.2.0", "zod": "^3.23.0"}, "devDependencies": {"@mattverse/configs": "workspace:*", "electron": "^33.0.0", "electron-builder": "^25.0.0", "electron-vite": "^2.3.0", "sass": "^1.80.0"}}
{"name": "@mattverse/mattverse-flow", "version": "1.1.0", "private": true, "description": "Workflow engine and flow visualization for Mattverse applications", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./core": {"import": "./dist/core.js", "types": "./dist/core.d.ts"}, "./types": {"import": "./dist/types.js", "types": "./dist/types.d.ts"}}, "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts,.tsx,.vue", "lint:fix": "eslint src --ext .ts,.tsx,.vue --fix", "typecheck": "vue-tsc --noEmit"}, "dependencies": {"@mattverse/shared": "workspace:*", "vue": "^3.5.0", "@vue-flow/core": "^1.45.0", "@vue-flow/controls": "^1.1.2", "@vue-flow/minimap": "^1.5.0", "@vue-flow/background": "^1.3.0", "@vue-flow/node-resizer": "^1.4.0", "@vue-flow/node-toolbar": "^1.1.0", "zod": "^3.23.0"}, "devDependencies": {"@mattverse/configs": "workspace:*"}, "peerDependencies": {"vue": "^3.5.0"}}
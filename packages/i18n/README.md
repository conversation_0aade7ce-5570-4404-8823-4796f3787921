# @mattverse/i18n

Mattverse 项目的国际化共享包，支持中文和英文双语。

## 📁 目录结构

```
packages/i18n/
├── src/
│   ├── index.ts                    # 主入口文件
│   ├── types.ts                    # TypeScript 类型定义
│   └── locales/                    # 语言包目录
│       ├── zh-CN/                  # 中文语言包
│       │   ├── index.ts            # 中文主入口
│       │   ├── common.ts           # 通用词汇
│       │   ├── navigation.ts       # 导航相关
│       │   ├── components.ts       # 组件相关
│       │   ├── messages.ts         # 系统消息
│       │   ├── workflow.ts         # 工作流相关
│       │   └── settings.ts         # 设置相关
│       └── en-US/                  # 英文语言包
│           ├── index.ts            # 英文主入口
│           ├── common.ts           # 通用词汇
│           ├── navigation.ts       # 导航相关
│           ├── components.ts       # 组件相关
│           ├── messages.ts         # 系统消息
│           ├── workflow.ts         # 工作流相关
│           └── settings.ts         # 设置相关
├── package.json
├── tsconfig.json
├── tsup.config.ts
└── README.md
```

## 🌍 支持的语言

- 🇨🇳 **简体中文** (`zh-CN`) - 默认语言
- 🇺🇸 **英文** (`en-US`)

## 📝 语言包分类

### 1. `common.ts` - 通用词汇
包含最常用的操作词汇，如：确认、取消、保存、删除等

### 2. `navigation.ts` - 导航相关
包含所有导航菜单项的翻译

### 3. `components.ts` - 组件相关
按组件分类的翻译，如：
- 侧边栏组件
- 语言选择器
- 主题选择器
- 表格组件
- 表单组件
- 对话框组件
- 文件上传组件
- 搜索组件

### 4. `messages.ts` - 系统消息
包含各种系统提示消息：
- 成功消息
- 失败消息
- 确认消息
- 错误消息
- 警告消息

### 5. `workflow.ts` - 工作流相关
工作流功能的专用翻译

### 6. `settings.ts` - 设置相关
设置页面的专用翻译

## 🚀 使用方法

### 基础使用

```typescript
import { useI18n } from '@mattverse/i18n'

// 在组件中使用
const { t } = useI18n()

// 使用翻译
t('common.confirm')           // "确认" / "Confirm"
t('navigation.workflow')      // "工作流" / "Workflow"
t('components.sidebar.toggle') // "切换侧边栏" / "Toggle Sidebar"
```

### 在 Vue 模板中使用

```vue
<template>
  <div>
    <h1>{{ $t('workflow.title') }}</h1>
    <button>{{ $t('common.save') }}</button>
  </div>
</template>
```

### 带参数的翻译

```typescript
// 语言包中定义
{
  "table": {
    "total_items": "共 {count} 项"  // 中文
    "total_items": "Total {count} items"  // 英文
  }
}

// 使用
t('components.table.total_items', { count: 10 })
```

## 🔧 添加新翻译

### 1. 添加新的翻译键

在对应的分类文件中添加新的翻译键：

```typescript
// packages/i18n/src/locales/zh-CN/common.ts
export default {
  // 现有翻译...
  new_key: '新翻译',
}

// packages/i18n/src/locales/en-US/common.ts
export default {
  // 现有翻译...
  new_key: 'New Translation',
}
```

### 2. 更新类型定义

在 `types.ts` 中更新对应的接口：

```typescript
export interface MessageSchema {
  common: {
    // 现有类型...
    new_key: string
  }
  // 其他分类...
}
```

### 3. 添加新的分类

如果需要添加新的翻译分类：

1. 创建新的分类文件：
   ```
   packages/i18n/src/locales/zh-CN/new-category.ts
   packages/i18n/src/locales/en-US/new-category.ts
   ```

2. 在各语言的 `index.ts` 中导入：
   ```typescript
   import newCategory from './new-category'
   
   export default {
     // 现有导出...
     newCategory,
   }
   ```

3. 更新类型定义：
   ```typescript
   export interface MessageSchema {
     // 现有类型...
     newCategory: {
       // 新分类的类型定义
     }
   }
   ```

## 📦 构建

```bash
# 构建 i18n 包
pnpm build:i18n

# 开发模式（监听文件变化）
cd packages/i18n
pnpm dev
```

## 🔍 类型安全

该包提供完整的 TypeScript 类型支持：

- `SupportedLocale` - 支持的语言类型
- `LocaleConfig` - 语言配置接口
- `MessageSchema` - 语言包结构类型

所有翻译键都有类型提示和检查，确保不会出现拼写错误。

## 🎯 最佳实践

1. **保持键名一致**：确保中英文使用相同的键名
2. **分类清晰**：将相关的翻译放在同一个分类文件中
3. **命名规范**：使用下划线分隔的小写命名
4. **及时更新类型**：添加新翻译时同步更新类型定义
5. **避免嵌套过深**：建议最多 3 层嵌套

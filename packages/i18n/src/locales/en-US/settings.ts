// Settings related
export default {
  // Basic settings
  title: 'System Settings',
  language: 'Language Settings',
  theme: 'Theme Settings',
  appearance: 'Appearance Settings',
  notifications: 'Notification Settings',
  advanced: 'Advanced Settings',
  
  // Language settings
  language_options: {
    auto: 'Auto Detect',
    zh_cn: '简体中文',
    en_us: 'English',
  },
  
  // Theme settings
  theme_options: {
    light: 'Light Theme',
    dark: 'Dark Theme',
    system: 'Follow System',
  },
  
  // Notification settings
  notification_options: {
    enable_notifications: 'Enable Notifications',
    desktop_notifications: 'Desktop Notifications',
    sound_notifications: 'Sound Notifications',
    email_notifications: 'Email Notifications',
  },
  
  // Advanced settings
  advanced_options: {
    debug_mode: 'Debug Mode',
    auto_save: 'Auto Save',
    backup_frequency: 'Backup Frequency',
    cache_size: 'Cache Size',
    log_level: 'Log Level',
  },
  
  // About information
  about: {
    title: 'About',
    version: 'Version',
    build: 'Build Version',
    license: 'License',
    copyright: 'Copyright',
    website: 'Official Website',
    support: 'Technical Support',
  },
}

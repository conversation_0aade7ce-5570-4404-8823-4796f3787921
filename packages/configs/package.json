{"name": "@mattverse/configs", "version": "1.1.0", "private": true, "description": "Shared configuration files for Mattverse monorepo", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./eslint": "./eslint.config.js", "./prettier": "./prettier.config.js", "./tsconfig": "./tsconfig.json", "./typescript/*": "./src/typescript/*", "./tailwind": {"import": "./dist/tailwind.js", "types": "./dist/tailwind.d.ts"}, "./styles": {"import": "./dist/styles.js", "types": "./dist/styles.d.ts"}, "./src/*": "./src/*"}, "files": ["dist", "*.config.js", "*.config.json", "tsconfig.json"], "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "typecheck": "tsc --noEmit"}, "devDependencies": {"electron-vite": "^2.3.0", "vue-eslint-parser": "^9.4.0"}, "peerDependencies": {"vue": "^3.5.0", "@vueuse/core": "^11.0.0", "vite": "^5.4.0", "@vitejs/plugin-vue": "^5.0.0", "@tailwindcss/vite": "^4.1.11", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "tailwindcss": "^4.1.11", "lucide-vue-next": "^0.447.0"}}
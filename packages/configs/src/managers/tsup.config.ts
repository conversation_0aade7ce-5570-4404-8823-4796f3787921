import type { Options } from 'tsup'

/**
 * Tsup 配置选项接口
 */
export interface TsupConfigOptions extends Partial<Options> {
  /** 入口文件 */
  entry?: string[]
  /** 输出格式 */
  format?: ('esm' | 'cjs' | 'iife')[]
  /** 外部依赖 */
  external?: string[]
  /** 目标平台 */
  platform?: 'node' | 'browser' | 'neutral'
  /** 目标版本 */
  target?: string
}

/**
 * 基础 tsup 配置
 * 为所有包提供统一的构建配置
 */
export const createTsupConfig = (options: TsupConfigOptions = {}): Options => {
  const {
    entry = ['src/index.ts'],
    format = ['esm'],
    external = [],
    platform = 'neutral',
    target = 'es2022',
    ...customOptions
  } = options

  return {
    entry,
    format,
    dts: true,
    clean: true,
    sourcemap: true,
    minify: false,
    external: ['vue', 'electron', ...external],
    treeshake: true,
    splitting: false,
    outDir: 'dist',
    platform,
    target,
    ...customOptions,
  }
}

/**
 * 包专用配置
 */
export const packageConfigs = {
  // 共享工具包配置
  shared: createTsupConfig({
    external: [
      'dayjs',
      'nanoid',
      'zod',
      '@grpc/grpc-js',
      '@grpc/proto-loader',
      'protobufjs',
      'electron',
    ],
    platform: 'node',
    format: ['esm'],
  }),

  // UI 组件包配置（备用，主要使用 Vite 构建）
  'mattverse-ui': createTsupConfig({
    target: 'es2020',
    external: [
      'vue',
      '@vue/*',
      '@vueuse/*',
      '@mattverse/shared',
      'radix-vue',
      'lucide-vue-next',
      'vue-sonner',
      'class-variance-authority',
      'clsx',
      'tailwind-merge',
      'tailwindcss-animate',
    ],
    platform: 'neutral',
    format: ['esm'],
  }),

  // 工作流引擎配置
  'mattverse-flow': createTsupConfig({
    external: ['vue', '@vue-flow/*', '@vueuse/*', '@mattverse/shared', 'zod'],
    platform: 'neutral',
    format: ['esm'],
  }),

  // Electron 核心配置
  'electron-core': createTsupConfig({
    external: [
      'electron',
      'electron-log',
      'electron-devtools-installer',
      '@mattverse/shared',
      '@grpc/grpc-js',
      '@grpc/proto-loader',
      'protobufjs',
    ],
    platform: 'node',
    format: ['esm', 'cjs'],
  }),

  // 国际化包配置
  i18n: createTsupConfig({
    external: ['vue', 'vue-i18n'],
    platform: 'neutral',
    format: ['esm', 'cjs'],
  }),

  // 配置包配置
  configs: createTsupConfig({
    external: [
      // Vue 相关
      'vue',
      '@vue/*',
      '@vueuse/core',

      // Electron 相关
      'electron',
      'electron-vite',

      // Vite 相关
      'vite',
      '@vitejs/plugin-vue',
      '@tailwindcss/vite',
      'rollup',

      // Tailwind 相关
      'tailwindcss',
      'tailwindcss/plugin',
      'tailwindcss-animate',

      // 自动导入相关
      'unplugin-auto-import',
      'unplugin-vue-components',

      // ESLint 相关
      'eslint',
      'eslint-plugin-vue',
      '@eslint/js',
      '@typescript-eslint/eslint-plugin',
      '@typescript-eslint/parser',
      'vue-eslint-parser',

      // TypeScript 相关
      'typescript',
      '@typescript-eslint/*',

      // 工具库
      'lodash',
      'lodash/merge',
      'tsup',

      // Node.js 内置模块
      'path',
      'fs',
      'url',
      'tty',
      'util',
      'os',
      'crypto',
      'stream',
      'events',
      'buffer',
      'process',
    ],
    platform: 'node',
  }),
}

// 默认导出基础配置
export default createTsupConfig()

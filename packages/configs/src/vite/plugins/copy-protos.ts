/**
 * 复制 Proto 文件插件
 * 自动将 proto 文件从源目录复制到构建输出目录
 */
import { copyFileSync, mkdirSync, existsSync, readdirSync, statSync } from 'fs'
import { resolve } from 'path'
import type { Plugin } from 'vite'

// 文件复制缓存，避免重复复制
const copyCache = new Map<string, number>()

export interface CopyProtosOptions {
  /** 源目录路径，相对于项目根目录 */
  sourceDir?: string
  /** 目标目录路径，相对于输出目录 */
  targetDir?: string
  /** 要复制的文件模式，支持通配符或具体文件名数组 */
  files?: string[] | 'all'
  /** 是否显示复制日志 */
  verbose?: boolean
}

/**
 * 创建复制 proto 文件的插件
 */
export function createCopyProtosPlugin(options: CopyProtosOptions = {}): Plugin {
  const {
    sourceDir = '../../packages/electron-core/src/grpc/protos',
    targetDir = 'out/main/protos',
    files = 'all',
    verbose = true,
  } = options

  return {
    name: 'copy-protos',
    generateBundle() {
      // 解析源目录和目标目录的绝对路径
      const resolvedSourceDir = resolve(process.cwd(), sourceDir)
      const resolvedTargetDir = resolve(process.cwd(), targetDir)

      // 确保源目录存在
      if (!existsSync(resolvedSourceDir)) {
        if (verbose) {
          console.warn(`⚠️  源目录不存在: ${resolvedSourceDir}`)
        }
        return
      }

      // 确保目标目录存在
      if (!existsSync(resolvedTargetDir)) {
        mkdirSync(resolvedTargetDir, { recursive: true })
      }

      // 获取要复制的文件列表
      let filesToCopy: string[] = []

      if (files === 'all') {
        // 自动扫描所有 .proto 文件
        try {
          const allFiles = readdirSync(resolvedSourceDir)
          filesToCopy = allFiles.filter(file => file.endsWith('.proto'))
        } catch (error) {
          console.error(`❌ 读取源目录失败: ${resolvedSourceDir}`, error)
          return
        }
      } else {
        // 使用指定的文件列表
        filesToCopy = files
      }

      // 复制文件（带缓存优化）
      let copiedCount = 0
      filesToCopy.forEach(file => {
        const sourcePath = resolve(resolvedSourceDir, file)
        const targetPath = resolve(resolvedTargetDir, file)

        if (existsSync(sourcePath)) {
          try {
            // 检查文件是否需要更新
            const sourceStats = statSync(sourcePath)
            const cacheKey = `${sourcePath}->${targetPath}`
            const cachedMtime = copyCache.get(cacheKey)

            // 如果目标文件存在且源文件未修改，跳过复制
            if (existsSync(targetPath) && cachedMtime === sourceStats.mtimeMs) {
              return
            }

            copyFileSync(sourcePath, targetPath)
            copyCache.set(cacheKey, sourceStats.mtimeMs)
            copiedCount++
            if (verbose) {
              console.log(`✅ 复制 proto 文件: ${file} -> ${targetPath}`)
            }
          } catch (error) {
            console.error(`❌ 复制文件失败: ${file}`, error)
          }
        } else {
          if (verbose) {
            console.warn(`⚠️  文件不存在: ${sourcePath}`)
          }
        }
      })

      if (verbose && copiedCount > 0) {
        console.log(`🎉 成功复制 ${copiedCount} 个 proto 文件到 ${resolvedTargetDir}`)
      }
    }
  }
}

/**
 * 预设配置
 */
export const copyProtosConfigs = {
  /** 默认配置 - 复制所有 proto 文件 */
  default: () => createCopyProtosPlugin(),

  /** 自定义配置 */
  custom: (options: CopyProtosOptions) => createCopyProtosPlugin(options),
}

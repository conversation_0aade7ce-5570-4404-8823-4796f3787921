import { resolve } from 'node:path'
import { defineConfig, externalizeDepsPlugin, type ElectronViteConfig } from 'electron-vite'
import vue from '@vitejs/plugin-vue'
import tailwindcss from '@tailwindcss/vite'

/**
 * Electron 配置选项接口
 */
export interface ElectronConfigOptions {
  /** 应用名称 */
  appName?: string
  /** 主进程入口文件 */
  mainEntry?: string
  /** 预加载脚本入口文件 */
  preloadEntry?: string
  /** 渲染进程入口文件 */
  rendererEntry?: string
  /** 渲染进程 HTML 模板 */
  rendererTemplate?: string
  /** 路径别名 */
  alias?: Record<string, string>
  /** 其他自定义选项 */
  [key: string]: any
}

/**
 * 基础 Electron Vite 配置
 * 为所有 Electron 应用提供统一的配置
 */
export const createElectronConfig = (options: ElectronConfigOptions = {}): ElectronViteConfig => {
  const {
    appName = '',
    mainEntry = '../../packages/main/src/index.ts',
    preloadEntry = '../../packages/preload/src/index.ts',
    rendererEntry = 'src/renderer/src/main.ts',
    rendererTemplate = 'src/renderer/index.html',
    alias = {},
    ...customOptions
  } = options

  const baseAlias = {
    '@': resolve(process.cwd(), 'src/renderer/src'),
    '@mattverse/shared': resolve(process.cwd(), '../../packages/shared/src'),
    '@mattverse/ui': resolve(process.cwd(), '../../packages/mattverse-ui/src'),
    '@mattverse/flow': resolve(process.cwd(), '../../packages/mattverse-flow/src'),
    '@mattverse/configs': resolve(process.cwd(), '../../packages/configs/src'),
    ...alias,
  }

  return defineConfig({
    main: {
      plugins: [externalizeDepsPlugin()],
      build: {
        outDir: 'dist/main',
        rollupOptions: {
          input: {
            index: resolve(process.cwd(), mainEntry),
          },
        },
      },
    },
    preload: {
      plugins: [externalizeDepsPlugin()],
      build: {
        outDir: 'dist/preload',
        rollupOptions: {
          input: {
            index: resolve(process.cwd(), preloadEntry),
          },
        },
      },
    },
    renderer: {
      resolve: {
        alias: baseAlias,
      },
      plugins: [vue(), tailwindcss()],
      build: {
        rollupOptions: {
          input: {
            index: resolve(process.cwd(), rendererTemplate),
          },
        },
      },
    },
    ...customOptions,
  })
}

/**
 * 预设配置工厂函数
 * 应用可以使用这些工厂函数创建自己的配置
 */
export const electronBuildPresets = {
  /** 默认配置 */
  default: (appName: string, options: Omit<ElectronConfigOptions, 'appName'> = {}) =>
    createElectronConfig({ appName, ...options }),

  /** 自定义配置 */
  custom: (options: ElectronConfigOptions) =>
    createElectronConfig(options),
}

// 默认导出基础配置
export default createElectronConfig()

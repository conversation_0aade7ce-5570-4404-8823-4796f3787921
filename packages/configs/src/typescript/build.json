{"$schema": "https://json.schemastore.org/tsconfig", "extends": "./base.json", "compilerOptions": {"//": "================严格检查（构建时更严格）=================", "outDir": "./dist", "rootDir": "./src", "baseUrl": ".", "//:": "============输出设置===============", "noEmit": false, "declaration": true, "declarationMap": true, "sourceMap": true, "emitDeclarationOnly": true, "removeComments": true, "importHelpers": true, "inlineSources": false, "stripInternal": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "exactOptionalPropertyTypes": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "build", "out", "release", "**/*.test.*", "**/*.spec.*", "**/*.d.ts", "vite.config.ts", "vitest.config.ts", "tailwind.config.ts"]}
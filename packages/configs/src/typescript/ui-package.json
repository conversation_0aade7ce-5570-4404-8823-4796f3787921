{"$schema": "https://json.schemastore.org/tsconfig", "extends": "./vue.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "baseUrl": ".", "noEmit": false, "declaration": true, "declarationMap": true, "emitDeclarationOnly": false, "removeComments": false, "preserveConstEnums": true, "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*.ts", "src/**/*.vue", "src/**/*.tsx", "vite.config.ts", "tailwind.config.ts", "components.json"], "exclude": ["node_modules", "dist", "build", "**/*.test.*", "**/*.spec.*", "vite.config.ts"]}
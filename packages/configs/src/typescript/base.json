{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "bundler", "strict": false, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "exactOptionalPropertyTypes": false, "noImplicitReturns": true, "noImplicitOverride": true, "noUncheckedIndexedAccess": true, "allowJs": true, "checkJs": false, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "importHelpers": true, "skipLibCheck": true, "verbatimModuleSyntax": false}, "exclude": ["node_modules", "dist", "build", "out", "release", "**/*.test.*", "**/*.spec.*", "**/*.d.ts"]}
/**
 * gRPC 服务类型定义
 */

// 基础响应状态
export interface ResponseStatus {
  code: number
  message: string
  success: boolean
}

// 注册请求
export interface RegisterRequest {
  user_id: string
  token: string
  server_name: string
  url: string
  region: string
  version: string
  access_level: number
  protocol_type: string
  is_force_to_register: boolean
  service_name_list: string[]
  service_version_list: string[]
  service_access_level_list: number[]
  server_type: string
}

// 注册响应
export interface RegisterResponse {
  status: ResponseStatus
  message: string
  server_id: string
}

// 通用请求
export interface GeneralRequest {
  user_id: string
  token: string
  service_name: string
  method_name: string
  params: Record<string, any>
  request_id?: string
  timeout?: number
}

// 通用响应
export interface GeneralResponse {
  status: ResponseStatus
  data: any
  request_id?: string
  timestamp: number
}

// 提交响应
export interface SubmitResponse {
  status: ResponseStatus
  task_id: string
  message: string
}

// Ping 请求
export interface PingRequest {
  timestamp: number
  client_id?: string
}

// Ping 响应
export interface PingResponse {
  status: ResponseStatus
  timestamp: number
  server_time: number
  latency: number
}

// 数据库请求
export interface DbRequest {
  user_id: string
  token: string
  operation: string
  table_name: string
  data?: Record<string, any>
  conditions?: Record<string, any>
  options?: Record<string, any>
}

// 数据库响应
export interface DbResponse {
  status: ResponseStatus
  data: any[]
  total_count?: number
  affected_rows?: number
}

// 获取请求
export interface GetRequest {
  user_id: string
  token: string
  resource_type: string
  resource_id?: string
  filters?: Record<string, any>
  pagination?: {
    page: number
    page_size: number
  }
}

// 操作请求
export interface OperateRequest {
  user_id: string
  token: string
  operation: string
  target_id: string
  params?: Record<string, any>
}

// 操作响应
export interface OperateResponse {
  status: ResponseStatus
  message: string
  result?: any
}

// 任务结果响应
export interface TaskResultResponse {
  status: ResponseStatus
  message: string
  result: string
}

// 文件块
export interface FileChunk {
  file_name: string
  user_id: string
  total_sha256: string
  chunk_id: number
  content: Uint8Array
  is_last: boolean
}

// 上传响应
export interface UploadResponse {
  status: ResponseStatus
  message: string
  upload_id: string
  stored_filepath: string
  total_chunks: number
  total_bytes: number
}

// 下载请求
export interface DownloadRequest {
  user_id: string
  token: string
  stored_filepath: string
  chunk_size: number
}

// 会话请求
export interface SessionRequest {
  user_id: string
  token: string
  session_id?: string
  session_name?: string
  session_type?: string
  metadata?: Record<string, any>
}

// 会话追加请求
export interface SessionAppendRequest {
  user_id: string
  token: string
  session_id: string
  message_type: string
  content: string
  metadata?: Record<string, any>
}

// 用户请求
export interface UserRequest {
  user_id?: string
  username?: string
  password?: string
  email?: string
  token?: string
  operation?: string
  user_data?: Record<string, any>
}

// Agent 请求
export interface AgentRequest {
  user_id: string
  token: string
  agent_type: string
  command: string
  params?: Record<string, any>
  stream_id?: string
}

// Agent 响应
export interface AgentResponse {
  status: ResponseStatus
  agent_type: string
  response_type: string
  data: any
  stream_id?: string
  is_final: boolean
}

// 客户端 API 请求
export interface ClientApiRequest {
  user_id: string
  token: string
  api_name: string
  method: string
  params?: Record<string, any>
  headers?: Record<string, string>
}

// 会话命令
export interface SessionCommand {
  user_id: string
  token: string
  session_id: string
  command_type: string
  command_data: Record<string, any>
}

// 服务器操作响应
export interface ServerOperateResponse {
  status: ResponseStatus
  server_info: {
    server_id: string
    cpu_usage: number
    memory_usage: number
    disk_usage: number
    network_io: {
      bytes_sent: number
      bytes_recv: number
    }
    active_connections: number
    uptime: number
  }
}

// 获取客户端 URL 响应
export interface GetClientUrlResponse {
  status: ResponseStatus
  client_url: string
  websocket_url?: string
  api_version: string
}

// gRPC 客户端配置
export interface GrpcClientConfig {
  host: string
  port: number
  secure?: boolean
  credentials?: any
  options?: Record<string, any>
}

// 连接状态
export enum ConnectionState {
  IDLE = 'IDLE',
  CONNECTING = 'CONNECTING',
  READY = 'READY',
  TRANSIENT_FAILURE = 'TRANSIENT_FAILURE',
  SHUTDOWN = 'SHUTDOWN'
}

// 流状态
export interface StreamState {
  isActive: boolean
  messageCount: number
  lastActivity: number
  error?: Error
}

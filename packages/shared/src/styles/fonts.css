@font-face {
  font-family: 'AlibabaPuHuiTi';
  src: url('../assets/fonts/AlibabaPuHuiTi-3-55-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'AlibabaSansHK';
  src: url('../assets/fonts/AlibabaSansHK-55.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* Noto Sans SC Regular 字体 */
@font-face {
  font-family: 'NotoSansSC';
  src: url('../assets/fonts/NotoSansSC-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'NotoSansSC';
  src: url('../assets/fonts/NotoSansSC-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'NotoSansSC';
  src: url('../assets/fonts/NotoSansSC-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'NotoSansSC';
  src: url('../assets/fonts/NotoSansSC-ExtraBold.ttf') format('truetype');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'NotoSansSC';
  src: url('../assets/fonts/NotoSansSC-Black.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SourceHanSans';
  src: url('../assets/fonts/SourceHanSans-VF.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SourceHanSansHC';
  src: url('../assets/fonts/SourceHanSansHC-VF.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SourceHanSansHK';
  src: url('../assets/fonts/SourceHanSansHK-VF.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SourceHanSansK';
  src: url('../assets/fonts/SourceHanSansK-VF.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SourceHanSansSC';
  src: url('../assets/fonts/SourceHanSansSC-VF.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SourceHanSansTC';
  src: url('../assets/fonts/SourceHanSansTC-VF.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

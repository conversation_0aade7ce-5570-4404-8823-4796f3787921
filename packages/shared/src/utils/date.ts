/**
 * 格式化日期时间字符串
 * @param dateString ISO 格式的日期字符串或 Date 对象
 * @param format 'YYYY/MM/DD HH:mm:ss' | 'YYYY-MM-DD' | 'HH:mm:ss'
 * @returns 格式化后的字符串
 */
export function formatDate(
  dateString: string | Date,
  format: 'YYYY/MM/DD HH:mm:ss' | 'YYYY-MM-DD' | 'HH:mm:ss' = 'YYYY/MM/DD HH:mm:ss',
): string {
  try {
    const date = new Date(dateString)

    // 检查日期是否有效
    if (Number.isNaN(date.getTime())) {
      return ''
    }

    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')

    switch (format) {
      case 'YYYY/MM/DD HH:mm:ss':
        return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`
      case 'YYYY-MM-DD':
        return `${year}-${month}-${day}`
      case 'HH:mm:ss':
        return `${hours}:${minutes}:${seconds}`
      default:
        return ''
    }
  } catch (error) {
    console.error('Invalid date string provided to formatDate:', error)
    return ''
  }
}

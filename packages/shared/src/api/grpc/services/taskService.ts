/**
 * 任务相关的 gRPC 服务
 */
import { BaseService } from './baseService'
import type { GetRequest, DbResponse, TaskResultResponse } from '../../../types/grpc'

export type GetTaskListRequest = Pick<GetRequest, 'user_id' | 'token'> & {
  id: string
}



export type GetTaskListResponse = DbResponse

export class TaskService extends BaseService {

  /**
   * 获取任务列表
   * @param params 请求参数
   * @returns Promise<DbResponse>
   */
  async getTaskList(params: GetTaskListRequest): Promise<DbResponse> {
    return this.call<DbResponse>('getTaskList', params)
  }

  /**
   * 获取任务结果
   * @param params 请求参数
   * @returns Promise<TaskResultResponse>
   */
  async getTaskResult(params: GetTaskListRequest): Promise<TaskResultResponse> {
    return this.call<TaskResultResponse>('getTaskResult', params)
  }

  /**
   * 删除任务
   * @param params 请求参数
   * @returns Promise<DbResponse>
   */
  async deleteTask(params: GetTaskListRequest): Promise<DbResponse> {
    return this.call<DbResponse>('deleteTask', params)
  }

  /**
   * 停止任务
   * @param params 请求参数
   * @returns Promise<DbResponse>
   */
  async stopTask(params: GetTaskListRequest): Promise<DbResponse> {
    return this.call<DbResponse>('stopTask', params)
  }
}

// 导出单例实例
export const taskService = new TaskService()

/**
 * 服务器相关的 gRPC 服务
 */
import { BaseService } from './baseService'
import type { GetRequest, DbResponse } from '../../../types/grpc'

export type GetServerListRequest = Pick<GetRequest, 'user_id' | 'token'> & {
  id: string
}

export type GetServerListResponse = DbResponse

export class ServerService extends BaseService {
  /**
   * 获取服务器列表
   * @param params 请求参数
   * @returns Promise<GetServerListResponse>
   */
  async getServerList(params: GetServerListRequest): Promise<GetServerListResponse> {
    return this.call<GetServerListResponse>('getServerList', params)
  }

}

// 导出单例实例
export const serverService = new ServerService()

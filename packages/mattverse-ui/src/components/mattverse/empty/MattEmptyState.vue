<template>
  <div class="flex flex-col items-center justify-center text-center py-12">
    <!-- 图标 -->
    <div class="p-4 rounded-full bg-muted/50 mb-4">
      <MattIcon
        :name="icon"
        :class="cn('text-muted-foreground', iconClass)"
        :style="{ width: iconSize + 'px', height: iconSize + 'px' }"
      />
    </div>

    <!-- 标题 -->
    <h3 class="text-lg font-semibold mb-2">{{ title }}</h3>

    <!-- 描述 -->
    <p class="text-muted-foreground mb-6 max-w-sm" v-if="description">
      {{ description }}
    </p>

    <!-- 操作按钮 -->
    <div v-if="$slots.action" class="flex gap-2">
      <slot name="action" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { cn } from '@/lib/utils'
import MattIcon from '../common/icon/MattIcon.vue'

interface Props {
  /** 图标名称 */
  icon?: string
  /** 图标大小 */
  iconSize?: number
  /** 图标样式类 */
  iconClass?: string
  /** 标题 */
  title: string
  /** 描述文本 */
  description?: string
}

withDefaults(defineProps<Props>(), {
  icon: 'FileX',
  iconSize: 48,
  iconClass: '',
})
</script>

<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { PanelLeftOpen, PanelRightOpen } from 'lucide-vue-next'
import { cn } from '@/lib/utils'
import { But<PERSON> } from '@/components/ui/button'
import { useSidebar } from './utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()

const { toggleSidebar, open } = useSidebar()
</script>

<template>
  <Button
    data-sidebar="trigger"
    data-slot="sidebar-trigger"
    variant="ghost"
    size="icon"
    :class="cn('h-7 w-7', props.class)"
    @click="toggleSidebar"
  >
    <!-- 根据侧边栏状态显示不同图标：展开时显示 PanelRightOpen，收起时显示 PanelLeftOpen -->
    <PanelRightOpen v-if="open" />
    <PanelLeftOpen v-else />
    <span class="sr-only">Toggle Sidebar</span>
  </Button>
</template>

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'MattverseUIComponents',
      fileName: (format) => `index.${format}.js`,
      formats: ['es', 'cjs'],
    },
    rollupOptions: {
      external: [
        'vue',
        'reka-ui',
        'class-variance-authority',
        'clsx',
        'tailwind-merge',
        'lucide-vue-next',
        'radix-vue',
        'vue-sonner',
        'tailwindcss-animate',
        '@mattverse/shared',
      ],
      output: {
        globals: {
          vue: 'Vue',
          'reka-ui': 'RekaUI',
          'class-variance-authority': 'ClassVarianceAuthority',
          clsx: 'clsx',
          'tailwind-merge': 'tailwindMerge',
          'lucide-vue-next': 'LucideVueNext',
          'radix-vue': 'RadixVue',
          'vue-sonner': 'VueSonner',
        },
        assetFileNames: (assetInfo) => {
          if (assetInfo.name?.endsWith('.css')) {
            return 'style.css'
          }
          return assetInfo.name || 'assets/[name].[ext]'
        }
      },
    },
    cssCodeSplit: true,
    sourcemap: true,
  },
})

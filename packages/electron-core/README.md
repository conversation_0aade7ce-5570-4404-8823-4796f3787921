# @mattverse/electron-core

Mattverse 项目的 Electron 核心功能包，提供统一的主进程、预加载脚本和 IPC 通信管理。

## 📑 目录

- [安装](#-安装)
- [快速开始](#-快速开始)
  - [主进程使用](#主进程使用)
  - [预加载脚本使用](#预加载脚本使用)
  - [渲染进程使用](#渲染进程使用)
- [API 参考](#-api-参考)
- [内置 IPC 通道](#-内置-ipc-通道)
- [使用示例](#-使用示例)
- [类型支持](#-类型支持)
- [最佳实践](#-最佳实践)
- [高级功能](#-高级功能)
- [架构设计](#️-架构设计)
- [注意事项](#-注意事项)
- [版本历史](#-版本历史)
- [贡献](#-贡献)

## 📦 安装

```bash
pnpm add @mattverse/electron-core
```

## ✨ 特性

- 🏭 **工厂模式**：简化 Electron 应用创建
- 🔒 **类型安全**：完整的 TypeScript 支持
- 🔌 **IPC 管理**：统一的进程间通信
- 🛡️ **安全性**：基于 contextBridge 的安全 API
- 📝 **日志系统**：跨进程的统一日志
- 🔧 **可扩展**：支持自定义 IPC 通道

## 🚀 快速开始

### 主进程使用

```typescript
// src/main/index.ts
import { createElectronApp, logger } from '@mattverse/electron-core'

const app = createElectronApp({
  window: {
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      preload: join(__dirname, '../preload/index.js')
    }
  },
  handlers: {
    // 自定义 IPC 处理器
    'my-app:get-config': async () => {
      return {
        appName: 'MyApp',
        version: '1.0.0',
        features: ['feature1', 'feature2']
      }
    }
  },
  onReady: () => {
    logger.info('应用已准备就绪!')
  },
  onWindowCreated: (window) => {
    if (process.env.NODE_ENV === 'development') {
      window.webContents.openDevTools()
    }
  }
})

// 启动应用
app.start().catch((error) => {
  logger.error('应用启动失败:', error)
  process.exit(1)
})
```

### 预加载脚本使用

```typescript
// src/preload/index.ts
import { contextBridge, ipcRenderer } from 'electron'
import { createPreloadBridge, logger } from '@mattverse/electron-core'

// 创建自定义 API
const customAPI = {
  // 应用特定配置
  getConfig: () => ipcRenderer.invoke('my-app:get-config'),
  
  // 系统信息
  platform: process.platform,
  versions: process.versions,
  
  // 自定义功能
  myApp: {
    doSomething: (data: any) => ipcRenderer.invoke('my-app:do-something', data)
  }
}

// 使用预加载桥接工厂
const api = createPreloadBridge(customAPI)

// 暴露 API 到渲染进程
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electronAPI', api)
    logger.info('API 暴露成功')
  } catch (error) {
    logger.error('API 暴露失败:', error)
  }
} else {
  // @ts-ignore
  window.electronAPI = api
}

export type ElectronAPI = typeof api
```

### 渲染进程使用

```typescript
// src/renderer/src/composables/useElectron.ts
export function useElectron() {
  const getAppInfo = async () => {
    try {
      const info = await window.electronAPI['app:get-info']()
      return info
    } catch (error) {
      console.error('获取应用信息失败:', error)
      throw error
    }
  }

  const getWindowState = async () => {
    try {
      const state = await window.electronAPI['window:get-state']()
      return state
    } catch (error) {
      console.error('获取窗口状态失败:', error)
      throw error
    }
  }

  return {
    getAppInfo,
    getWindowState
  }
}
```

## 🔧 API 参考

### createElectronApp(config)

创建 Electron 应用实例。

#### 参数

**ElectronAppConfig 接口：**

```typescript
interface ElectronAppConfig {
  window?: {
    width?: number              // 窗口宽度，默认 1200
    height?: number             // 窗口高度，默认 800
    minWidth?: number           // 最小宽度
    minHeight?: number          // 最小高度
    show?: boolean              // 是否立即显示，默认 false
    autoHideMenuBar?: boolean   // 是否自动隐藏菜单栏，默认 true
    icon?: string               // 应用图标路径
    webPreferences?: {
      preload?: string          // 预加载脚本路径
      sandbox?: boolean         // 是否启用沙箱，默认 false
      contextIsolation?: boolean // 是否启用上下文隔离，默认 true
      nodeIntegration?: boolean  // 是否启用 Node 集成，默认 false
    }
  }
  handlers?: Record<string, IPCChannelHandler>  // 自定义 IPC 处理器
  onReady?: () => void                          // 应用就绪回调
  onWindowCreated?: (window: BrowserWindow) => void  // 窗口创建回调
}
```

#### 返回值

返回应用实例，包含以下方法：
- `start()` - 启动应用
- `getMainWindow()` - 获取主窗口
- `quit()` - 退出应用
- `restart()` - 重启应用

### createPreloadBridge(customAPI)

创建预加载桥接 API。

#### 参数

- `customAPI` - 自定义 API 对象

#### 返回值

返回完整的 API 对象，包含：
- 内置的类型安全 IPC API
- 事件监听 API
- 自定义 API
- 便捷方法

## 📋 内置 IPC 通道

### 应用相关
- `app:get-version` - 获取应用版本
- `app:get-info` - 获取应用信息
- `app:quit` - 退出应用
- `app:restart` - 重启应用

### 窗口相关
- `window:minimize` - 最小化窗口
- `window:maximize` - 最大化/还原窗口
- `window:close` - 关闭窗口
- `window:get-state` - 获取窗口状态
- `window:set-size` - 设置窗口大小
- `window:center` - 居中窗口

### 对话框相关
- `dialog:select-file` - 选择文件
- `dialog:select-folder` - 选择文件夹
- `dialog:save-file` - 保存文件
- `dialog:show-message` - 显示消息

### 日志相关
- `logger:info` - 记录信息
- `logger:warn` - 记录警告
- `logger:error` - 记录错误
- `logger:debug` - 记录调试信息

## 🎯 使用示例

### 示例 1：内部应用 (Mattverse)

**主进程配置** (`apps/mattverse/src/main/index.ts`)：

```typescript
import { createElectronApp, logger } from '@mattverse/electron-core'
import { join, dirname } from 'node:path'
import { fileURLToPath } from 'node:url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const app = createElectronApp({
  window: {
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      preload: join(__dirname, '../preload/index.js')
    }
  },
  handlers: {
    // Mattverse 特定的配置获取
    'mattverse:get-config': async () => {
      return {
        appName: 'Mattverse',
        version: '1.1.0',
        features: ['workflow', 'ai', 'automation']
      }
    },
    // 工作流相关处理器
    'mattverse:get-workflows': async () => {
      // 从数据库或文件系统获取工作流列表
      return [
        { id: '1', name: '数据处理流程', status: 'active' },
        { id: '2', name: 'AI 分析流程', status: 'draft' }
      ]
    },
    'mattverse:save-workflow': async (workflow) => {
      // 保存工作流到数据库
      logger.info('保存工作流:', workflow)
      return { success: true, id: workflow.id }
    }
  },
  onReady: () => {
    logger.info('Mattverse 应用已启动!')
  }
})

app.start().catch((error) => {
  logger.error('Mattverse 启动失败:', error)
  process.exit(1)
})
```

**预加载脚本** (`apps/mattverse/src/preload/index.ts`)：

```typescript
import { contextBridge, ipcRenderer } from 'electron'
import { createPreloadBridge, logger } from '@mattverse/electron-core'

const customAPI = {
  // Mattverse 特定配置
  getConfig: () => ipcRenderer.invoke('mattverse:get-config'),

  // 系统信息
  platform: process.platform,
  versions: process.versions,

  // Mattverse 工作流功能
  mattverse: {
    getWorkflows: () => ipcRenderer.invoke('mattverse:get-workflows'),
    saveWorkflow: (workflow: any) => ipcRenderer.invoke('mattverse:save-workflow', workflow),
    deleteWorkflow: (id: string) => ipcRenderer.invoke('mattverse:delete-workflow', id)
  }
}

const api = createPreloadBridge(customAPI)

if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electronAPI', api)
    logger.info('Mattverse APIs 暴露成功')
  } catch (error) {
    logger.error('Mattverse APIs 暴露失败:', error)
  }
} else {
  // @ts-ignore
  window.electronAPI = api
}

export type ElectronAPI = typeof api
```

### 示例 2：豪鹏应用 (HighPower)

**主进程配置** (`apps/highpower/src/main/index.ts`)：

```typescript
import { createElectronApp, logger } from '@mattverse/electron-core'

const app = createElectronApp({
  window: {
    width: 1400,
    height: 900,
    minWidth: 1000,
    minHeight: 700
  },
  handlers: {
    // HighPower 应用配置
    'highpower:get-config': async () => {
      return {
        appName: 'HighPower',
        version: '1.1.0',
        features: ['high-performance', 'computing', 'gpu-acceleration']
      }
    },

    // 计算任务管理
    'compute:start-job': async (jobConfig) => {
      logger.info('启动计算任务:', jobConfig)
      const jobId = `job_${Date.now()}`

      // 这里可以集成实际的计算引擎
      // 例如：启动 Python 脚本、调用 CUDA 库等

      return {
        jobId,
        status: 'started',
        config: jobConfig,
        estimatedTime: jobConfig.iterations * 0.1 // 估算时间
      }
    },

    'compute:get-status': async (jobId: string) => {
      // 查询任务状态（可以从任务队列或数据库获取）
      return {
        jobId,
        status: 'running',
        progress: Math.random() * 100, // 模拟进度
        currentIteration: Math.floor(Math.random() * 100),
        estimatedTimeRemaining: Math.random() * 300
      }
    },

    // GPU 信息获取
    'highpower:get-gpu-info': async () => {
      // 这里可以调用 nvidia-ml-py 或其他 GPU 监控库
      return {
        gpuCount: 1,
        gpus: [{
          name: 'NVIDIA GeForce RTX 4080',
          memory: { total: '16GB', used: '4GB', free: '12GB' },
          utilization: Math.floor(Math.random() * 100),
          temperature: Math.floor(Math.random() * 40) + 40,
          powerDraw: Math.floor(Math.random() * 200) + 100
        }]
      }
    },

    // 系统资源监控
    'highpower:get-system-resources': async () => {
      const os = require('os')
      return {
        cpu: {
          model: os.cpus()[0].model,
          cores: os.cpus().length,
          usage: Math.random() * 100,
          temperature: Math.floor(Math.random() * 30) + 40
        },
        memory: {
          total: Math.round(os.totalmem() / 1024 / 1024 / 1024) + 'GB',
          used: Math.round((os.totalmem() - os.freemem()) / 1024 / 1024 / 1024) + 'GB',
          free: Math.round(os.freemem() / 1024 / 1024 / 1024) + 'GB',
          usage: ((os.totalmem() - os.freemem()) / os.totalmem() * 100).toFixed(1) + '%'
        }
      }
    }
  },
  onReady: () => {
    logger.info('HighPower 高性能计算应用已启动!')
  }
})
```

**渲染进程使用** (`apps/highpower/src/renderer/src/composables/useCompute.ts`)：

```typescript
// Vue 组合式函数示例
export function useCompute() {
  const currentJob = ref<string>('')
  const jobStatus = ref<any>(null)
  const isComputing = ref(false)

  // 启动计算任务
  const startComputeJob = async (config: any) => {
    try {
      isComputing.value = true
      const result = await window.electronAPI.compute.startJob(config)
      currentJob.value = result.jobId

      // 开始轮询任务状态
      pollJobStatus()

      return result
    } catch (error) {
      console.error('启动计算任务失败:', error)
      throw error
    }
  }

  // 轮询任务状态
  const pollJobStatus = async () => {
    if (!currentJob.value) return

    try {
      const status = await window.electronAPI.compute.getStatus(currentJob.value)
      jobStatus.value = status

      if (status.status === 'running') {
        // 继续轮询
        setTimeout(pollJobStatus, 1000)
      } else {
        isComputing.value = false
      }
    } catch (error) {
      console.error('获取任务状态失败:', error)
      isComputing.value = false
    }
  }

  // 获取系统资源信息
  const getSystemResources = async () => {
    try {
      return await window.electronAPI.highpower.getSystemResources()
    } catch (error) {
      console.error('获取系统资源失败:', error)
      throw error
    }
  }

  return {
    currentJob: readonly(currentJob),
    jobStatus: readonly(jobStatus),
    isComputing: readonly(isComputing),
    startComputeJob,
    getSystemResources
  }
}
```

## 🔍 类型支持

包提供完整的 TypeScript 类型支持：

```typescript
import type { 
  ElectronAppConfig, 
  IPCChannelName, 
  IPCChannelHandler 
} from '@mattverse/electron-core'
```

## 📝 最佳实践

1. **安全性**：始终使用 `contextBridge` 暴露 API，禁用 `nodeIntegration`
2. **类型安全**：充分利用 TypeScript 类型检查
3. **错误处理**：在 IPC 处理器中添加适当的错误处理
4. **日志记录**：使用内置的 logger 进行日志记录
5. **模块化**：将复杂的 IPC 处理器拆分到单独的模块中

## 🔧 高级功能

### 自定义 IPC 处理器

```typescript
// 在主进程中注册自定义处理器
import { IPCHandlerFactory } from '@mattverse/electron-core'

// 单个注册
IPCHandlerFactory.register('my-channel', async (data) => {
  // 处理逻辑
  return { success: true, data }
})

// 批量注册
IPCHandlerFactory.registerAll({
  'channel-1': async () => { /* ... */ },
  'channel-2': async (param) => { /* ... */ }
})
```

### 事件监听

```typescript
// 在渲染进程中监听事件
const unsubscribe = window.electronAPI.on('my-event', (data) => {
  console.log('收到事件:', data)
})

// 取消监听
unsubscribe()

// 监听一次
window.electronAPI.once('my-event', (data) => {
  console.log('收到一次性事件:', data)
})
```

### 日志系统

```typescript
import { logger } from '@mattverse/electron-core'

// 不同级别的日志
logger.info('信息日志')
logger.warn('警告日志')
logger.error('错误日志')
logger.debug('调试日志')
logger.verbose('详细日志')
```

## 🏗️ 架构设计

### 模块结构

```
@mattverse/electron-core/
├── src/
│   ├── app/
│   │   └── factory.ts          # 应用工厂
│   ├── handlers/
│   │   └── factory.ts          # IPC 处理器工厂
│   ├── preload/
│   │   └── api-factory.ts      # 预加载 API 工厂
│   ├── types/
│   │   └── ipc.ts             # IPC 类型定义
│   ├── utils/
│   │   └── logger.ts          # 日志工具
│   └── index.ts               # 入口文件
├── dist/                      # 构建输出
├── package.json
└── README.md
```

### 设计原则

1. **工厂模式**：使用工厂函数创建应用和 API
2. **类型安全**：提供完整的 TypeScript 类型支持
3. **可扩展性**：支持自定义 IPC 通道和处理器
4. **安全性**：通过 contextBridge 安全地暴露 API
5. **统一性**：为所有 Mattverse 应用提供一致的基础架构

## 🚨 注意事项

### 安全考虑

- 始终使用 `contextBridge` 暴露 API
- 禁用 `nodeIntegration`
- 启用 `contextIsolation`
- 验证来自渲染进程的数据

### 性能优化

- 避免在 IPC 处理器中执行耗时操作
- 使用异步操作处理 I/O
- 合理使用日志级别

### 错误处理

```typescript
// 在 IPC 处理器中添加错误处理
'my-channel': async (data) => {
  try {
    const result = await someAsyncOperation(data)
    return { success: true, result }
  } catch (error) {
    logger.error('操作失败:', error)
    throw new Error(`操作失败: ${error.message}`)
  }
}
```

## 🔄 版本历史

### v1.1.0
- 添加了类型安全的 IPC API
- 修复了日志系统在预加载脚本中的问题
- 支持自定义 IPC 通道

### v1.0.0
- 初始版本
- 基础的应用工厂和预加载桥接功能

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

### 开发环境设置

```bash
# 克隆项目
git clone <repository-url>

# 安装依赖
pnpm install

# 构建包
pnpm build

# 运行测试应用
pnpm dev:mattverse
pnpm dev:highpower
```

## 🔧 故障排除

### 常见问题

**Q: 预加载脚本中出现 "process is not defined" 错误**

A: 在渲染进程中无法直接访问 Node.js 的 `process` 对象。应该通过 IPC 从主进程获取相关信息：

```typescript
// ❌ 错误做法
const arch = process.arch

// ✅ 正确做法
const appInfo = await window.electronAPI['app:get-info']()
const arch = appInfo.arch
```

**Q: IPC 调用失败，提示 "channel not found"**

A: 确保在主进程中正确注册了 IPC 处理器：

```typescript
// 在 createElectronApp 的 handlers 中添加
handlers: {
  'my-custom-channel': async (data) => {
    return { result: 'success' }
  }
}
```

**Q: 日志不显示或报错**

A: 确保使用正确的日志方法：

```typescript
// ✅ 使用内置 logger
import { logger } from '@mattverse/electron-core'
logger.info('这是一条日志')

// ❌ 避免在预加载脚本中直接使用 console
console.log('这可能不会显示')
```

### 调试技巧

1. **启用开发者工具**：在 `onWindowCreated` 回调中调用 `window.webContents.openDevTools()`
2. **查看日志文件**：日志文件通常位于用户数据目录的 `logs` 文件夹中
3. **使用 logger.debug**：在开发环境中启用详细日志输出

## 🌟 更多模块使用案例

### 案例 1：数据库管理模块

**主进程数据库处理器**：

```typescript
// apps/database-manager/src/main/index.ts
import { createElectronApp, logger } from '@mattverse/electron-core'
import Database from 'better-sqlite3'

const db = new Database('app.db')

const app = createElectronApp({
  handlers: {
    // 数据库连接管理
    'db:connect': async (dbPath: string) => {
      try {
        const newDb = new Database(dbPath)
        logger.info('数据库连接成功:', dbPath)
        return { success: true, message: '连接成功' }
      } catch (error) {
        logger.error('数据库连接失败:', error)
        throw new Error(`连接失败: ${error.message}`)
      }
    },

    // 执行查询
    'db:query': async (sql: string, params: any[] = []) => {
      try {
        const stmt = db.prepare(sql)
        const result = stmt.all(...params)
        return { success: true, data: result }
      } catch (error) {
        logger.error('查询执行失败:', error)
        throw new Error(`查询失败: ${error.message}`)
      }
    },

    // 执行更新
    'db:execute': async (sql: string, params: any[] = []) => {
      try {
        const stmt = db.prepare(sql)
        const result = stmt.run(...params)
        return {
          success: true,
          changes: result.changes,
          lastInsertRowid: result.lastInsertRowid
        }
      } catch (error) {
        logger.error('执行失败:', error)
        throw new Error(`执行失败: ${error.message}`)
      }
    },

    // 获取表结构
    'db:get-schema': async (tableName: string) => {
      try {
        const result = db.prepare(`PRAGMA table_info(${tableName})`).all()
        return { success: true, schema: result }
      } catch (error) {
        throw new Error(`获取表结构失败: ${error.message}`)
      }
    }
  }
})
```

**渲染进程数据库操作组合函数**：

```typescript
// apps/database-manager/src/renderer/src/composables/useDatabase.ts
export function useDatabase() {
  const isConnected = ref(false)
  const currentDb = ref<string>('')
  const queryResult = ref<any[]>([])
  const loading = ref(false)

  // 连接数据库
  const connectDatabase = async (dbPath: string) => {
    try {
      loading.value = true
      const result = await window.electronAPI.invoke('db:connect', dbPath)
      if (result.success) {
        isConnected.value = true
        currentDb.value = dbPath
      }
      return result
    } catch (error) {
      console.error('连接数据库失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 执行查询
  const executeQuery = async (sql: string, params: any[] = []) => {
    try {
      loading.value = true
      const result = await window.electronAPI.invoke('db:query', sql, params)
      if (result.success) {
        queryResult.value = result.data
      }
      return result
    } catch (error) {
      console.error('查询失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    isConnected: readonly(isConnected),
    currentDb: readonly(currentDb),
    queryResult: readonly(queryResult),
    loading: readonly(loading),
    connectDatabase,
    executeQuery
  }
}
```

### 案例 2：文件处理模块

**主进程文件处理器**：

```typescript
// apps/file-processor/src/main/index.ts
import { createElectronApp, logger } from '@mattverse/electron-core'
import { promises as fs } from 'fs'
import path from 'path'
import { createReadStream, createWriteStream } from 'fs'

const app = createElectronApp({
  handlers: {
    // 文件读取
    'file:read': async (filePath: string, encoding: string = 'utf-8') => {
      try {
        const content = await fs.readFile(filePath, encoding as any)
        const stats = await fs.stat(filePath)
        return {
          success: true,
          content,
          size: stats.size,
          modified: stats.mtime,
          extension: path.extname(filePath)
        }
      } catch (error) {
        throw new Error(`读取文件失败: ${error.message}`)
      }
    },

    // 文件写入
    'file:write': async (filePath: string, content: string, encoding: string = 'utf-8') => {
      try {
        await fs.writeFile(filePath, content, encoding as any)
        const stats = await fs.stat(filePath)
        return {
          success: true,
          size: stats.size,
          path: filePath
        }
      } catch (error) {
        throw new Error(`写入文件失败: ${error.message}`)
      }
    },

    // 批量文件处理
    'file:batch-process': async (files: string[], operation: string) => {
      const results = []
      for (const file of files) {
        try {
          let result
          switch (operation) {
            case 'compress':
              // 实现文件压缩逻辑
              result = await compressFile(file)
              break
            case 'convert':
              // 实现文件格式转换逻辑
              result = await convertFile(file)
              break
            default:
              throw new Error(`不支持的操作: ${operation}`)
          }
          results.push({ file, success: true, result })
        } catch (error) {
          results.push({ file, success: false, error: error.message })
        }
      }
      return { success: true, results }
    },

    // 文件监控
    'file:watch': async (dirPath: string) => {
      const chokidar = require('chokidar')
      const watcher = chokidar.watch(dirPath)

      watcher.on('change', (filePath) => {
        // 发送文件变更事件到渲染进程
        mainWindow?.webContents.send('file:changed', {
          type: 'change',
          path: filePath,
          timestamp: Date.now()
        })
      })

      return { success: true, message: '文件监控已启动' }
    }
  }
})

// 辅助函数
async function compressFile(filePath: string) {
  // 实现文件压缩逻辑
  return { compressed: true, originalSize: 1000, compressedSize: 500 }
}

async function convertFile(filePath: string) {
  // 实现文件转换逻辑
  return { converted: true, outputPath: filePath.replace('.txt', '.json') }
}
```

### 案例 3：网络请求模块

**主进程网络处理器**：

```typescript
// apps/network-client/src/main/index.ts
import { createElectronApp, logger } from '@mattverse/electron-core'
import axios from 'axios'

const app = createElectronApp({
  handlers: {
    // HTTP 请求
    'http:request': async (config: any) => {
      try {
        const response = await axios(config)
        return {
          success: true,
          data: response.data,
          status: response.status,
          headers: response.headers
        }
      } catch (error) {
        logger.error('HTTP 请求失败:', error)
        throw new Error(`请求失败: ${error.message}`)
      }
    },

    // 文件下载
    'http:download': async (url: string, savePath: string, onProgress?: Function) => {
      try {
        const response = await axios({
          method: 'GET',
          url,
          responseType: 'stream'
        })

        const writer = createWriteStream(savePath)
        const totalLength = response.headers['content-length']
        let downloadedLength = 0

        response.data.on('data', (chunk: Buffer) => {
          downloadedLength += chunk.length
          const progress = (downloadedLength / totalLength) * 100

          // 发送下载进度到渲染进程
          mainWindow?.webContents.send('download:progress', {
            url,
            progress: Math.round(progress),
            downloaded: downloadedLength,
            total: totalLength
          })
        })

        response.data.pipe(writer)

        return new Promise((resolve, reject) => {
          writer.on('finish', () => {
            resolve({ success: true, path: savePath })
          })
          writer.on('error', reject)
        })
      } catch (error) {
        throw new Error(`下载失败: ${error.message}`)
      }
    },

    // API 批量请求
    'http:batch': async (requests: any[]) => {
      try {
        const results = await Promise.allSettled(
          requests.map(config => axios(config))
        )

        return {
          success: true,
          results: results.map((result, index) => ({
            index,
            success: result.status === 'fulfilled',
            data: result.status === 'fulfilled' ? result.value.data : null,
            error: result.status === 'rejected' ? result.reason.message : null
          }))
        }
      } catch (error) {
        throw new Error(`批量请求失败: ${error.message}`)
      }
    }
  }
})
```

### 案例 4：系统集成模块

**主进程系统集成处理器**：

```typescript
// apps/system-integration/src/main/index.ts
import { createElectronApp, logger } from '@mattverse/electron-core'
import { exec, spawn } from 'child_process'
import { promisify } from 'util'
import os from 'os'

const execAsync = promisify(exec)

const app = createElectronApp({
  handlers: {
    // 系统命令执行
    'system:exec': async (command: string, options: any = {}) => {
      try {
        const { stdout, stderr } = await execAsync(command, {
          timeout: 30000,
          ...options
        })
        return {
          success: true,
          stdout: stdout.trim(),
          stderr: stderr.trim()
        }
      } catch (error) {
        logger.error('命令执行失败:', error)
        throw new Error(`命令执行失败: ${error.message}`)
      }
    },

    // 进程管理
    'system:spawn': async (command: string, args: string[] = [], options: any = {}) => {
      return new Promise((resolve, reject) => {
        const child = spawn(command, args, {
          stdio: ['pipe', 'pipe', 'pipe'],
          ...options
        })

        let stdout = ''
        let stderr = ''

        child.stdout?.on('data', (data) => {
          stdout += data.toString()
          // 实时发送输出到渲染进程
          mainWindow?.webContents.send('process:stdout', {
            pid: child.pid,
            data: data.toString()
          })
        })

        child.stderr?.on('data', (data) => {
          stderr += data.toString()
          mainWindow?.webContents.send('process:stderr', {
            pid: child.pid,
            data: data.toString()
          })
        })

        child.on('close', (code) => {
          resolve({
            success: code === 0,
            code,
            stdout: stdout.trim(),
            stderr: stderr.trim(),
            pid: child.pid
          })
        })

        child.on('error', (error) => {
          reject(new Error(`进程启动失败: ${error.message}`))
        })
      })
    },

    // 系统信息获取
    'system:info': async () => {
      try {
        const info = {
          platform: os.platform(),
          arch: os.arch(),
          release: os.release(),
          hostname: os.hostname(),
          uptime: os.uptime(),
          loadavg: os.loadavg(),
          totalmem: os.totalmem(),
          freemem: os.freemem(),
          cpus: os.cpus().map(cpu => ({
            model: cpu.model,
            speed: cpu.speed,
            times: cpu.times
          })),
          networkInterfaces: os.networkInterfaces()
        }

        return { success: true, info }
      } catch (error) {
        throw new Error(`获取系统信息失败: ${error.message}`)
      }
    },

    // 环境变量管理
    'system:env': async (action: 'get' | 'set', key?: string, value?: string) => {
      try {
        switch (action) {
          case 'get':
            if (key) {
              return { success: true, value: process.env[key] }
            } else {
              return { success: true, env: process.env }
            }
          case 'set':
            if (key && value !== undefined) {
              process.env[key] = value
              return { success: true, message: `环境变量 ${key} 已设置` }
            } else {
              throw new Error('设置环境变量需要提供 key 和 value')
            }
          default:
            throw new Error(`不支持的操作: ${action}`)
        }
      } catch (error) {
        throw new Error(`环境变量操作失败: ${error.message}`)
      }
    },

    // 服务管理 (Windows/Linux)
    'system:service': async (action: 'start' | 'stop' | 'status', serviceName: string) => {
      try {
        let command = ''
        const platform = os.platform()

        switch (platform) {
          case 'win32':
            switch (action) {
              case 'start':
                command = `net start "${serviceName}"`
                break
              case 'stop':
                command = `net stop "${serviceName}"`
                break
              case 'status':
                command = `sc query "${serviceName}"`
                break
            }
            break
          case 'linux':
            command = `systemctl ${action} ${serviceName}`
            break
          default:
            throw new Error(`不支持的平台: ${platform}`)
        }

        const { stdout, stderr } = await execAsync(command)
        return {
          success: true,
          action,
          service: serviceName,
          output: stdout.trim(),
          error: stderr.trim()
        }
      } catch (error) {
        throw new Error(`服务操作失败: ${error.message}`)
      }
    }
  }
})
```

**渲染进程系统操作组合函数**：

```typescript
// apps/system-integration/src/renderer/src/composables/useSystem.ts
export function useSystem() {
  const systemInfo = ref<any>(null)
  const processOutput = ref<string[]>([])
  const isExecuting = ref(false)

  // 获取系统信息
  const getSystemInfo = async () => {
    try {
      const result = await window.electronAPI.invoke('system:info')
      if (result.success) {
        systemInfo.value = result.info
      }
      return result
    } catch (error) {
      console.error('获取系统信息失败:', error)
      throw error
    }
  }

  // 执行系统命令
  const executeCommand = async (command: string, options: any = {}) => {
    try {
      isExecuting.value = true
      processOutput.value = []

      const result = await window.electronAPI.invoke('system:exec', command, options)

      if (result.stdout) {
        processOutput.value.push(`[STDOUT] ${result.stdout}`)
      }
      if (result.stderr) {
        processOutput.value.push(`[STDERR] ${result.stderr}`)
      }

      return result
    } catch (error) {
      processOutput.value.push(`[ERROR] ${error.message}`)
      throw error
    } finally {
      isExecuting.value = false
    }
  }

  // 监听进程输出
  onMounted(() => {
    // 监听实时输出
    window.electronAPI.on('process:stdout', (data: any) => {
      processOutput.value.push(`[${data.pid}] ${data.data}`)
    })

    window.electronAPI.on('process:stderr', (data: any) => {
      processOutput.value.push(`[${data.pid}] ERROR: ${data.data}`)
    })
  })

  return {
    systemInfo: readonly(systemInfo),
    processOutput: readonly(processOutput),
    isExecuting: readonly(isExecuting),
    getSystemInfo,
    executeCommand
  }
}
```

## 🔗 模块间协作示例

### 跨模块数据共享

```typescript
// 在主进程中设置全局数据共享
import { createElectronApp } from '@mattverse/electron-core'

// 全局状态管理
const globalState = {
  currentUser: null,
  appSettings: {},
  sharedData: new Map()
}

const app = createElectronApp({
  handlers: {
    // 全局状态管理
    'global:get-state': async (key: string) => {
      return { success: true, value: globalState[key] }
    },

    'global:set-state': async (key: string, value: any) => {
      globalState[key] = value
      // 广播状态变更到所有窗口
      BrowserWindow.getAllWindows().forEach(window => {
        window.webContents.send('global:state-changed', { key, value })
      })
      return { success: true }
    },

    // 模块间通信
    'module:communicate': async (fromModule: string, toModule: string, data: any) => {
      // 实现模块间消息传递
      const targetWindow = BrowserWindow.getAllWindows().find(
        window => window.webContents.getTitle().includes(toModule)
      )

      if (targetWindow) {
        targetWindow.webContents.send('module:message', {
          from: fromModule,
          data
        })
        return { success: true, delivered: true }
      } else {
        return { success: false, error: '目标模块未找到' }
      }
    }
  }
})
```

## 📄 许可证

MIT License

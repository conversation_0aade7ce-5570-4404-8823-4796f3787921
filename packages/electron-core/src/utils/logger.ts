/**
 * 统一的日志工具
 */

// 检查是否在主进程中
const isMainProcess = typeof process !== 'undefined' && process.type === 'browser'

let log: any = null

// 只在主进程中导入 electron-log
if (isMainProcess) {
  try {
    log = require('electron-log')
    // 配置日志
    log.transports.console.level = 'debug'
    log.transports.file.level = 'info'
    log.transports.file.maxSize = 5 * 1024 * 1024 // 5MB
  } catch (error) {
    console.warn('Failed to load electron-log:', error)
  }
}

export const logger = {
  info: (message: string, ...args: unknown[]) => {
    if (log) {
      log.info(message, ...args)
    } else {
      console.info(`[INFO] ${message}`, ...args)
    }
  },
  warn: (message: string, ...args: unknown[]) => {
    if (log) {
      log.warn(message, ...args)
    } else {
      console.warn(`[WARN] ${message}`, ...args)
    }
  },
  error: (message: string, ...args: unknown[]) => {
    if (log) {
      log.error(message, ...args)
    } else {
      console.error(`[ERROR] ${message}`, ...args)
    }
  },
  debug: (message: string, ...args: unknown[]) => {
    if (log) {
      log.debug(message, ...args)
    } else {
      console.debug(`[DEBUG] ${message}`, ...args)
    }
  },
  verbose: (message: string, ...args: unknown[]) => {
    if (log) {
      log.verbose(message, ...args)
    } else {
      console.log(`[VERBOSE] ${message}`, ...args)
    }
  }
}

export default logger

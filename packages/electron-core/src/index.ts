/**
 * Electron Core 包入口
 */
export * from './grpc'

// 类型定义
export * from './types/ipc'

export * from './types/grpc'

// 处理器工厂
export { IPCHandlerFactory } from './handlers/factory'

// 预加载 API 工厂
export { createTypedAPI, createEventAPI, createPreloadBridge } from './preload/api-factory'

// 工具
export { logger } from './utils/logger'
export {
  createDevToolsManager,
  installVueDevTools,
  installReactDevTools,
  DEVTOOLS_EXTENSIONS,
  type DevToolsConfig,
  type DevToolsExtension,
} from './utils/devtools'

// 便捷的应用创建函数
export { createElectronApp } from './app/factory'

// 安全工具
export {
  generateCSP,
  generateCSPForHeader,
  generateCSPMetaTag,
  validateCSP,
  CSPPresets,
  type CSPOptions,
} from './security/csp'

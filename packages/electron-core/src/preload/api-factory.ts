/**
 * 预加载 API 工厂
 * 生成类型安全的预加载 API
 */
import { ipcRenderer } from 'electron'
import { IPCChannelName } from '../types/ipc'

/**
 * 创建类型安全的 API 对象
 */
export function createTypedAPI() {
  // 创建基础 API 对象
  const api = {} as Record<string, (...args: unknown[]) => Promise<unknown>>

  // 所有可用的 IPC 通道
  const channels: IPCChannelName[] = [
    // App 相关
    'app:get-version',
    'app:get-info',
    'app:quit',
    'app:restart',
    
    // Window 相关
    'window:minimize',
    'window:maximize',
    'window:close',
    'window:get-state',
    'window:set-size',
    'window:center',
    
    // Dialog 相关
    'dialog:select-file',
    'dialog:select-folder',
    'dialog:save-file',
    'dialog:show-message',
    
    // Store 相关
    'store:get',
    'store:set',
    'store:delete',
    'store:clear',
    'store:has',
    
    // File System 相关
    'fs:read-file',
    'fs:write-file',
    'fs:exists',
    'fs:mkdir',
    
    // Logger 相关
    'logger:info',
    'logger:warn',
    'logger:error',
    'logger:debug'
  ]

  // 动态生成 API 方法
  channels.forEach(channel => {
    api[channel] = ((...args: unknown[]) =>
      ipcRenderer.invoke(channel, ...args)
    )
  })

  return api
}

/**
 * 创建事件监听器
 */
export function createEventAPI() {
  return {
    /**
     * 监听事件
     */
    on: (channel: string, callback: (...args: unknown[]) => void) => {
      const subscription = (_event: unknown, ...args: unknown[]) => callback(...args)
      ipcRenderer.on(channel, subscription)
      return () => ipcRenderer.removeListener(channel, subscription)
    },

    /**
     * 监听一次事件
     */
    once: (channel: string, callback: (...args: unknown[]) => void) => {
      const subscription = (_event: unknown, ...args: unknown[]) => callback(...args)
      ipcRenderer.once(channel, subscription)
    },

    /**
     * 发送事件（不等待返回）
     */
    send: (channel: string, ...args: unknown[]) => {
      ipcRenderer.send(channel, ...args)
    },

    /**
     * 移除事件监听器
     */
    removeAllListeners: (channel: string) => {
      ipcRenderer.removeAllListeners(channel)
    }
  }
}

/**
 * 创建完整的预加载桥接
 */
export function createPreloadBridge(customAPI: Record<string, unknown> = {}) {
  const typedAPI = createTypedAPI()
  const eventAPI = createEventAPI()

  return {
    // 类型安全的 IPC API
    ...typedAPI,

    // 事件 API
    events: eventAPI,

    // 自定义 API
    ...customAPI,

    // 便捷方法
    invoke: (channel: string, ...args: unknown[]) => ipcRenderer.invoke(channel, ...args),
    send: (channel: string, ...args: unknown[]) => ipcRenderer.send(channel, ...args),
    on: eventAPI.on,
    once: eventAPI.once
  }
}

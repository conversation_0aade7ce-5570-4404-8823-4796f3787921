<h1>
  <img src="./packages/configs/src/assets/images/icons/mattverse/mattverse.ico" alt="图标" width="48" style="vertical-align: middle; margin-right: 8px;" />
  MattVerse 电池设计自动化平台
</h1>

<div align="center">

<img src="./packages/configs/src/assets/images/icons/mattverse/mattverse.png" alt="图标" width="100" />

**基于 Electron-Vite 应用程序 Monorepo+turborepo**

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Node.js](https://img.shields.io/badge/node-%3E%3D18-green.svg)](https://nodejs.org/)
[![pnpm](https://img.shields.io/badge/pnpm-%3E%3D9.0.0-orange.svg)](https://pnpm.io/)
[![Electron](https://img.shields.io/badge/electron-33.x-blue.svg)](https://electronjs.org/)

</div>

## 🚀 项目概述

MattVerse 电池设计自动化平台 是一个基于现代化技术栈的桌面应用程序集合，采用 monorepo 架构管理多个相关项目，提供高效的开发体验和代码复用。



## 一、技术栈

- **技术栈范围：**

### 1. 核心技术栈

| 类别         | 技术          | 版本   | 说明                          |
| ------------ | ------------- | ------ | ----------------------------- |
| **构建工具** | Turbo         | latest | Monorepo 构建系统             |
| **包管理**   | pnpm          | 9.0.0+ | 高效的包管理器                |
| **前端框架** | Vue 3         | 3.5.x  | 渐进式 JavaScript 框架        |
| **类型系统** | TypeScript    | 5.8.x  | JavaScript 的超集             |
| **桌面框架** | Electron      | 33.x   | 跨平台桌面应用开发框架        |
| **构建工具** | Electron-vite | 2.3.x  | Electron 专用的 Vite 构建工具 |
| **UI 框架**  | Tailwind CSS  | 4.1.x  | 实用优先的 CSS 框架           |
| **组件库**   | Radix Vue     | 1.9.x  | 无样式、可访问的组件库        |
| **版本管理** | Changesets    | 2.x    | 版本管理和发布工具            |

### 2. 开发工具与库

| 类别            | 技术                        | 版本    | 说明                      |
| --------------- | --------------------------- | ------- | ------------------------- |
| **状态管理**    | Pinia                       | 3.0.x   | Vue 3 状态管理库          |
| **持久化**      | pinia-plugin-persistedstate | 4.1.x   | Pinia 状态持久化插件      |
| **路由**        | Vue Router                  | 4.4.x   | Vue 官方路由管理器        |
| **工作流**      | Vue Flow                    | 1.45.x  | 可视化工作流组件          |
| **工具库**      | VueUse                      | 11.0.x  | Vue 组合式 API 工具集     |
| **图标**        | Lucide Vue Next             | 0.447.x | 美观的图标库              |
| **数据验证**    | Zod                         | 3.23.x  | TypeScript 优先的模式验证 |
| **表单验证**    | vee-validate                | 4.13.x  | Vue 表单验证库            |
| **通知**        | vue-sonner                  | 1.2.x   | 优雅的通知组件            |
| **数据库**      | Dexie                       | 4.0.x   | IndexedDB 包装器          |
| **HTTP 客户端** | Axios                       | 1.7.x   | Promise 基础的 HTTP 库    |
| **动画**        | GSAP                        | 3.12.x  | 高性能动画库              |
| **日期处理**    | Day.js                      | 1.11.x  | 轻量级日期库              |
| **ID 生成**     | nanoid                      | 5.0.x   | 小巧、安全的 ID 生成器    |

### 3. 后端通信

- **gRPC：** 使用 @grpc/grpc-js 进行高性能的客户端-服务器通信
- **消息打包：** 使用 @msgpack/msgpack 进行高效的数据序列化

### 4. 开发规范

- **组件风格：** 默认使用组合式 API (`<script setup>`)
- **代码风格：** 统一使用 ESLint + Prettier 进行代码格式化
- **提交规范：** 使用 Commitizen + Commitlint 规范提交信息
- **类型检查：** 全面使用 TypeScript 进行类型检查

---

## 二、Monorepo 架构说明

### 1. 架构优势

- **代码共享：** 通过 packages 实现代码复用，避免重复开发
- **统一构建：** 使用 Turbo 实现高效的并行构建和缓存
- **版本管理：** 使用 Changesets 进行统一的版本管理和发布
- **依赖管理：** 使用 pnpm workspace 实现高效的依赖管理
- **类型安全：** 跨包的 TypeScript 类型共享和检查

### 2. 包依赖关系

```mermaid
graph TD
    A[apps/mattverse] --> B[packages/shared]
    A --> C[packages/electron-core]
    A --> D[packages/mattverse-ui]
    A --> E[packages/mattverse-flow]
    A --> F[packages/configs]

    G[apps/highpower] --> B
    G --> C
    G --> D
    G --> E
    G --> F

    C --> B
    D --> B
    E --> B

    F -.-> H[构建配置]
    B -.-> I[基础工具]
```

### 3. 开发工作流

```bash
# 安装依赖
pnpm install

# 构建所有包
pnpm build

# 开发模式
pnpm dev:mattverse    # 启动 Mattverse 应用
pnpm dev:highpower    # 启动 HighPower 应用

# 构建应用
pnpm build:mattverse  # 构建 Mattverse 应用
pnpm build:highpower  # 构建 HighPower 应用

# 版本管理
pnpm changeset        # 创建变更集
pnpm version          # 更新版本号
pnpm release          # 发布包
```

---

## 三、项目开发流程（RIPER-5）

### R - Research（研究）

- **目标：**明确功能需求、交互设计、模块边界。
- **Vue 关注点：**
  - 是否为页面、业务组件或基础组件？。
  - 数据流如何设计？是否需要 composable 封装？。
- **Electron 重点：**
  - 功能由主进程实现还是渲染进程？。
  - 是否需要 IPC 通信？。
- **产出物：**
  - 需求文档
  - 验收标准（Acceptance Criteria）
  - 用户价值说明

---

### I - Investigate（调研）

- **目标：** 分析已有实现，提出至少两种解决方案。
- **Vue 调研：**
  - 是否已有可复用组件/composable？。
  - 是否存在技术风险（如性能瓶颈、重复逻辑）？。
- **Electron 调研：**
  - 分析渲染进程 (Renderer Process):
  		- 审视现有 Vue 组件、Composable 函数、Pinia Store 是否可复用。
  		- 评估新增组件的复杂度和状态管理方案。
  - 分析主进程 (Main Process):
    	- 审视现有 Node.js 模块是否能满足需求（如 fs, path, dialog）
    	- 评估是否需要引入新的 Node.js 依赖来增强原生能力。
  - 设计进程通信 (IPC):
    	- 明确 ipcMain 和 ipcRenderer 之间需要哪些通信通道
    	- 设计通过 contextBridge 暴露给渲染进程的 API 接口，确保安全。
  - 方案对比： 提出不少于两种方案，并从以下维度对比：
    	- **逻辑归属：**  该逻辑应放在主进程还是渲染进程？为什么？
    	- **性能影响：**  对应用启动速度、内存占用、UI 响应有何影响？
      	- **安全性：**  如何通过 contextBridge 隔离上下文，防止安全漏洞？
      	- **维护性：**  方案的耦合度如何？未来是否易于扩展？
- **产出物：**
  - ✅ 技术对比文档：包含方案的优劣势、风险评估、预估工时，并明确 IPC 通信协议。

---

### P - Plan（计划）

- **目标：** 将选定方案转化为清晰、可执行的开发任务列表。

- **行为规范：**
  - **拆解任务 (TODO List):** 必须按进程和模块进行拆分，例如：
    - `[主进程]` - 在 `main.js` 中实现文件读取的 IPC 监听。
    - `[预加载脚本]` - 在 `preload.js` 中通过 `contextBridge` 暴露 `window.api.readFile` 方法。
    - `[渲染进程-Store]` - 创建 `useFileStore` 的 Pinia 模块，管理文件状态。
    - `[渲染进程-组件]` - 开发 `<FileViewer.vue>` 组件，调用 API 并展示内容。
    - `[渲染进程-类型]` - 在 `types/` 目录下定义 IPC 传输的数据结构。
  - **明确优先级：** 标识出关键路径和依赖关系（如：主进程接口需优先完成）
  - **文档化：** 所有计划使用 Markdown 格式记录，便于追踪。
  
- 产出物：
	- ✅ 详细的任务计划 Markdown 文档 (例如: [007]实现本地图片预览功能计划.md)。
  
  

---

### E - Execute（执行）

- **目标：**高质量、高效率地完成编码，并确保代码的清晰度和健壮性。
- **Vue 最佳实践：**
  - 所有组件保持职责单一，避免页面组件逻辑过重。
  - 全面拥抱 **`<script setup>`** 和 **Composition API**，逻辑复用优先抽取到 `composables/` 目录。
  - 组件的 **Props** 和 **Events** 必须有明确的 **TypeScript** 类型定义。
  - 复杂或跨组件的状态，统一使用 **Pinia** 进行管理。
- **Electron 最佳实践：**
  - **严禁**开启 `nodeIntegration`，所有主进程能力必须通过安全的 `contextBridge` 暴露。
  - **最小化暴露原则**：只暴露渲染进程必需的 API，隐藏实现细节。
  - 主进程中的耗时操作（如 I/O）必须使用**异步**方法，避免阻塞 UI
- **代码注释：** 为核心函数、IPC 通道、复杂业务逻辑添加 JSDoc 格式的注释。
- **产出物：**
  -  **高质量、符合规范的代码**
  - **持续更新的计划文档**（标记已完成任务）。

### R - Review（审查）

- **目标：**  保证代码质量、功能正确性、文档完整性，并沉淀经验。
- **行为指引：**
  - ✅ 命名是否清晰，语义一致？
  - ✅ 是否重复造轮子？有无可复用逻辑？
  - ✅ 是否符合组件职责与边界设计？
  - ✅ 是否有必要的注释和类型定义？
  - ✅ IPC 通信是否封装清晰、异常处理完备？
- **Code Review (CR):** 审查者重点关注以下方面：
  - **进程边界：** 业务逻辑的划分是否合理？有没有渲染进程代码可以移到主进程？
  - **IPC 安全性：** `contextBridge` 的使用是否规范？暴露的 API 是否存在安全隐患？
  - **Vue 组件设计：** 组件是否遵循单一职责原则？Props 和 Events 设计是否清晰？
  - **状态管理：** Pinia Store 的设计是否合理？是否存在不必要的全局状态？
  - **性能：** 是否存在可能导致内存泄漏的操作？Vue 组件是否过度渲染？
  - **代码风格：** 是否遵循团队统一的 ESLint 和 Prettier 规则？
  - 新增的 **Vue Composable** 或 **Electron 工具函数**是否可以被其他功能复用？
  - 开发过程中遇到的坑点和解决方案，记录到团队知识库。
- **产出物：**
  - ✅ **CR 记录 (Pull Request)**。
  - ✅ **已归档的开发文档与测试报告**。
  - ✅ **最佳实践或可复用模块总结**

## 三、项目目录结构约定

```bash
mattverse-monorepo/
├── turbo.json                      # Turborepo 配置文件
├── package.json                    # 根 package.json
├── pnpm-workspace.yaml            # pnpm 工作空间配置
├── pnpm-lock.yaml                 # 锁定文件
├── .gitignore
├── README.md
├── commitlint.config.cjs          # Commitlint 配置
├── eslint.config.js               # ESLint 配置
├── prettier.config.js             # Prettier 配置
├── tsconfig.json                  # TypeScript 根配置
│
├── apps/                          # 应用程序目录
│   ├── mattverse/                 # Mattverse 主应用（功能完整版）
│   │   ├── src/
│   │   │   ├── main/              # 主进程
│   │   │   │   └── main.ts        # 主进程入口
│   │   │   ├── preload/           # 预加载脚本
│   │   │   │   └── preload.ts     # 预加载入口
│   │   │   ├── renderer/          # 渲染进程
│   │   │   │   ├── index.html     # HTML 入口
│   │   │   │   └── src/           # Vue 应用源码
│   │   │   │       ├── App.vue    # 根组件
│   │   │   │       ├── main.ts    # Vue 应用入口
│   │   │   │       ├── router/    # 路由配置
│   │   │   │       ├── store/     # Pinia 状态管理
│   │   │   │       ├── views/     # 页面组件
│   │   │   │       ├── components/ # 业务组件
│   │   │   │       ├── assets/    # 静态资源
│   │   │   │       └── styles/    # 样式文件
│   │   │   └── components/        # 共享组件
│   │   │       └── ui/            # UI 组件
│   │   ├── electron.vite.config.ts # Electron-Vite 配置
│   │   ├── package.json           # 应用依赖配置
│   │   ├── tsconfig.json          # TypeScript 配置
│   │   └── dist/                  # 构建输出
│   │
│   └── highpower/                 # HighPower 应用（高性能计算版）
│       ├── src/
│       │   ├── main/              # 主进程
│       │   │   └── main.ts        # 主进程入口
│       │   ├── preload/           # 预加载脚本
│       │   │   └── preload.ts     # 预加载入口
│       │   └── renderer/          # 渲染进程
│       │       ├── index.html     # HTML 入口
│       │       └── src/           # Vue 应用源码
│       │           ├── App.vue    # 根组件
│       │           ├── main.ts    # Vue 应用入口
│       │           └── router/    # 路由配置
│       ├── electron.vite.config.ts # Electron-Vite 配置
│       ├── package.json           # 应用依赖配置
│       ├── tsconfig.json          # TypeScript 配置
│       └── dist/                  # 构建输出
│
├── packages/                      # 共享包目录
│   ├── configs/                   # 共享配置包 (@mattverse/configs)
│   │   ├── src/
│   │   │   ├── build/             # 构建配置
│   │   │   │   └── tsup.base.ts   # TSup 基础配置
│   │   │   ├── vite/              # Vite 配置
│   │   │   │   ├── base.ts        # Vite 基础配置
│   │   │   │   ├── auto-import.ts # 自动导入配置
│   │   │   │   └── electron.base.ts # Electron-Vite 配置
│   │   │   ├── tailwind/          # Tailwind CSS 配置
│   │   │   │   ├── base.config.ts # 基础配置
│   │   │   │   ├── themes.ts      # 主题配置
│   │   │   │   └── plugins.ts     # 插件配置
│   │   │   ├── typescript/        # TypeScript 配置
│   │   │   │   └── vue.json       # Vue 项目配置
│   │   │   └── index.ts           # 统一导出
│   │   ├── eslint.config.js       # ESLint 配置
│   │   ├── prettier.config.js     # Prettier 配置
│   │   ├── tsconfig.json          # TypeScript 配置
│   │   ├── package.json
│   │   └── dist/                  # 构建输出
│   │
│   ├── shared/                    # 共享工具库 (@mattverse/shared)
│   │   ├── src/
│   │   │   ├── api/               # API 相关
│   │   │   ├── utils/             # 工具函数
│   │   │   ├── hooks/             # Vue Composables
│   │   │   ├── constants/         # 常量定义
│   │   │   ├── types/             # TypeScript 类型
│   │   │   ├── db/                # 数据库相关
│   │   │   └── index.ts           # 统一导出
│   │   ├── package.json
│   │   ├── tsconfig.json
│   │   └── dist/                  # 构建输出
│   │
│   ├── electron-core/             # Electron 核心功能 (@mattverse/electron-core)
│   │   ├── src/
│   │   │   ├── app/               # 应用工厂
│   │   │   │   └── factory.ts     # 应用创建工厂
│   │   │   ├── handlers/          # IPC 处理器
│   │   │   │   └── factory.ts     # 处理器工厂
│   │   │   ├── preload/           # 预加载 API
│   │   │   │   └── api-factory.ts # API 工厂
│   │   │   ├── types/             # 类型定义
│   │   │   │   └── ipc.ts         # IPC 类型
│   │   │   ├── utils/             # 工具函数
│   │   │   │   └── logger.ts      # 日志工具
│   │   │   └── index.ts           # 统一导出
│   │   ├── package.json
│   │   └── dist/                  # 构建输出
│   │
│   ├── mattverse-ui/              # UI 组件库 (@mattverse/mattverse-ui)
│   │   ├── src/
│   │   │   ├── components/        # Vue 组件
│   │   │   │   ├── ui/            # 基础 UI 组件
│   │   │   │   └── mattverse/     # 业务组件
│   │   │   ├── lib/               # 工具库
│   │   │   │   └── utils.ts       # 工具函数
│   │   │   ├── styles/            # 样式文件
│   │   │   │   └── index.css      # 主样式文件
│   │   │   └── index.ts           # 导出入口
│   │   ├── components.json        # shadcn-vue 配置
│   │   ├── tailwind.config.ts     # Tailwind 配置
│   │   ├── vite.config.ts         # Vite 配置
│   │   ├── package.json
│   │   ├── tsconfig.json
│   │   └── dist/                  # 构建输出
│   │
│   └── mattverse-flow/            # 工作流组件 (@mattverse/mattverse-flow)
│       ├── src/
│       │   ├── core/              # 核心逻辑
│       │   ├── nodes/             # 节点定义
│       │   ├── parser/            # 解析器
│       │   ├── types/             # 类型定义
│       │   └── index.ts           # 统一导出
│       ├── package.json
│       ├── tsconfig.json
│       └── dist/                  # 构建输出
│
├── scripts/                       # 构建脚本
│   ├── build-all.ps1             # Windows 构建脚本
│   ├── build-all.sh              # Unix 构建脚本
│   └── clean-all.ps1             # 清理脚本
│
└── docs/                          # 项目文档
    └── Vue 3 + Electron + Monorepo 项目.md


```

## 四、常用组件与代码约定

✅ 组件分类标准
| 模块路径                | 说明                                    | 依赖关系           |
| ----------------------- | --------------------------------------- | ------------------ |
| apps/mattverse          | Mattverse 主应用，功能完整，UI 复杂     | 依赖所有共享包     |
| apps/highpower          | HighPower 定制应用                      | 依赖所有共享包     |
| packages/configs        | 共享配置（Vite、ESLint、Tailwind 等）   | 无内部依赖         |
| packages/shared         | 共享工具库（utils、hooks、types 等）    | 基础包，无内部依赖 |
| packages/electron-core  | Electron 核心功能（主进程、预加载脚本） | 依赖 shared        |
| packages/mattverse-ui   | UI 组件库（基础组件、业务组件）         | 依赖 shared        |
| packages/mattverse-flow | 工作流组件（节点、解析器、核心逻辑）    | 依赖 shared        |

✅ Electron 通信约定

- 主进程 handler：

```ts
ipcMain.handle('device:getList', async () => { ... })
```

- 渲染进程调用：

```ts
await window.api.invoke('device:getList')
```

## 五、文档协作规范

项目文档存储 (`/docs` 或 `/project_document/`)

- ✅ 使用 Markdown 管理任务、会议纪要与阶段文档

- ✅ 所有开发节点（包括每阶段的开始与完成）都应写入文档

## 六、常用开发命令

### 1. 项目管理命令

```bash
# 安装依赖
pnpm install

# 清理所有构建缓存
pnpm clean:all

# 清理依赖并重新安装
pnpm clean:deps
```

### 2. 开发命令

```bash
# 启动开发环境
pnpm dev:mattverse          # 启动 Mattverse 应用
pnpm dev:highpower          # 启动 HighPower 应用

# 预览构建结果
pnpm preview:mattverse      # 预览 Mattverse 应用
pnpm preview:highpower      # 预览 HighPower 应用

# 开发包
pnpm dev:ui                 # 开发 UI 组件库
pnpm dev:flow               # 开发工作流组件
```

### 3. 构建命令

```bash
# 构建所有包
pnpm build

# 构建应用
pnpm build:mattverse        # 构建 Mattverse 应用
pnpm build:highpower        # 构建 HighPower 应用

# 构建包
pnpm build:shared           # 构建共享工具库
pnpm build:ui               # 构建 UI 组件库
pnpm build:flow             # 构建工作流组件
pnpm build:core             # 构建 Electron 核心

# 平台特定构建
pnpm build:mattverse:win    # 构建 Windows 版本
pnpm build:mattverse:mac    # 构建 macOS 版本
pnpm build:mattverse:linux  # 构建 Linux 版本
```

### 4. 代码质量命令

```bash
# 代码检查
pnpm lint                   # 检查所有包
pnpm lint:fix               # 自动修复代码问题

# 类型检查
pnpm typecheck              # 检查所有包的类型

# 格式化代码
pnpm format                 # 格式化所有代码
```

### 5. 版本管理命令

```bash
# 创建变更集
pnpm changeset

# 更新版本号
pnpm version

# 发布包
pnpm release
```

## 七、开发习惯建议

**作用：** 所有阶段成果的唯一可信来源。

**规则：**

- 每个任务的计划与结果必须保存为 Markdown 文件。
- 文件命名格式为 `[编号]任务简述.md`，如 `[015]主进程数据库模块重构.md`。
- 文档应包含：阶段说明、时间记录、变更记录、**关键代码片段或 PR 链接**。

## 八、五问原则（Five Whys）

当你遇到一个问题或需求模糊时，持续追问 “为什么”，直到挖出最底层原因：

```text
为什么要做这个功能？
→ 因为客户反馈搜索慢
→ 为什么会慢？
→ 因为每次都请求全量数据
→ 为什么要请求全量？
→ 因为前端没有分页设计
→ 为什么没有分页设计？
→ 因为之前默认数据量少……
```

## 九、项目特色功能

### 1. Monorepo 架构优势

- **统一构建系统：** 使用 Turbo 实现高效的并行构建和智能缓存
- **包依赖管理：** 通过 pnpm workspace 实现高效的依赖共享
- **版本统一管理：** 使用 Changesets 进行版本控制和发布管理
- **代码共享：** 核心逻辑通过 packages 实现跨应用复用

### 2. Electron 架构设计

- **进程分离：** 主进程、预加载脚本、渲染进程职责清晰分离
- **安全通信：** 通过 contextBridge 实现安全的 IPC 通信
- **核心功能抽象：** electron-core 包提供统一的 Electron 功能封装
- **多应用支持：** 支持 Mattverse 和 HighPower 两个不同定位的应用

### 3. 前端技术栈

- **现代化开发：** Vue 3 + TypeScript + Composition API
- **组件化设计：** 基于 Radix Vue 的无样式组件库
- **样式系统：** Tailwind CSS 4.x 实用优先的样式方案
- **状态管理：** Pinia + 持久化插件实现状态管理
- **工作流可视化：** Vue Flow 实现拖拽式工作流编辑

### 4. 开发体验优化

- **自动导入：** 组件和 API 自动导入，提升开发效率
- **类型安全：** 全面的 TypeScript 类型检查和推导
- **代码规范：** ESLint + Prettier + Commitlint 统一代码风格
- **热重载：** 开发环境下的快速热重载和实时预览

## 十、团队协作角色导向

在每个阶段中，请代入以下角色视角进行决策与执行：



### **🧠 PM（项目经理）**



- **关心事项：** 进度、风险、资源。
- **提醒你问自己：** 我有没有控制好节奏？主进程和渲染进程的开发进度是否协调？



### **🧩 PDM（产品经理）**



- **关心事项：** 用户价值、核心问题。
- **提醒你问自己：** 这个功能是否充分利用了桌面应用的优势？用户体验是否流畅自然？



### **🏗 AR（架构师）**



- **关心事项：** 技术选型、扩展性、稳定性。
- **提醒你问自己：** **我们的 IPC 通道设计是否高效且安全？这个新模块对应用启动时间和内存占用有什么影响？**



### **🛠 LD（开发负责人）**



- **关心事项：** 代码质量、可维护性、性能、安全。
- **提醒你问自己：** **我写的 Vue 组件是否易于复用？状态管理是否清晰？主进程和渲染进程的逻辑是否正确分离？**



### **📄 DW（文档管理者）**



- **关心事项：** 记录完备、历史可追溯。
- **提醒你问自己：** **如果新人接手，他能通过文档快速理解主进程、渲染进程和它们之间的通信机制吗？**

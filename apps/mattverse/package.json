{"name": "mattverse", "version": "1.1.0", "private": true, "description": "Mattverse - AI-powered workflow automation platform", "author": "mattverse.com", "homepage": "https://mattverse.com", "main": "out/main/index.js", "scripts": {"dev": "electron-vite dev", "build": "electron-vite build", "preview": "electron-vite preview", "build:win": "pnpm run build && electron-builder --win", "build:mac": "pnpm run build && electron-builder --mac", "build:linux": "pnpm run build && electron-builder --linux", "clean": "rimraf dist release", "lint": "eslint src --ext .ts,.tsx,.vue", "lint:fix": "eslint src --ext .ts,.tsx,.vue --fix", "typecheck": "vue-tsc --noEmit"}, "dependencies": {"@mattverse/electron-core": "workspace:*", "@mattverse/mattverse-flow": "workspace:*", "@mattverse/mattverse-ui": "workspace:*", "@mattverse/shared": "workspace:*", "@mattverse/i18n": "workspace:*", "highlight.js": "^11.11.1", "markdown-it": "^14.1.0"}, "devDependencies": {"@mattverse/configs": "workspace:*"}}
appId: com.mattverse.app
productName: MattVerse
copyright: Copyright © 2024 MattVerse.com

# 开发环境配置
forceCodeSigning: false
npmRebuild: false
# 完全禁用代码签名
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true

directories:
  output: release/${version}

files:
  - dist
  - out

# macOS 配置
mac:
  icon: ../../packages/configs/src/assets/images/icons/mattverse/mattverse.icns
  target:
    - target: dmg
      arch: [x64, arm64]
  artifactName: ${productName}-${version}-mac-${arch}.${ext}
  category: public.app-category.productivity

# Windows 配置 - 使用 zip 压缩包，完全禁用签名
win:
  icon: ../../packages/configs/src/assets/images/icons/mattverse/mattverse.ico
  target:
    - target: zip
      arch: [x64]
  artifactName: ${productName}-${version}-win-${arch}.${ext}
  # 完全禁用代码签名和验证
  verifyUpdateCodeSignature: false
  # 禁用签名
  signAndEditExecutable: false
  signDlls: false

# Linux 配置
linux:
  icon: ../../packages/configs/src/assets/images/icons/mattverse/mattverse.png
  target:
    - target: AppImage
      arch: [x64]
  artifactName: ${productName}-${version}-linux-${arch}.${ext}
  category: Office

# DMG 配置
dmg:
  icon: ../../packages/configs/src/assets/images/icons/mattverse/mattverse.icns
  iconSize: 100
  contents:
    - x: 410
      y: 150
      type: link
      path: /Applications
    - x: 130
      y: 150
      type: file

# 发布配置
publish:
  provider: generic
  url: https://releases.mattverse.com/

# 额外资源
extraResources:
  - from: ../../packages/configs/src/assets/images/icons/mattverse/
    to: icons/
    filter: ['*.png', '*.ico', '*.icns']

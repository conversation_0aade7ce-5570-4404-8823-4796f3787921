/**
 * Mattverse 预加载脚本
 */
import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { createPreloadBridge, logger } from '@mattverse/electron-core'

// 创建 Mattverse 特定的自定义 API
const customAPI = {
  // Mattverse 特定配置
  getConfig: () => ipcRenderer.invoke('mattverse:get-config'),

  // 系统信息
  platform: process.platform,
  versions: process.versions,

  // Mattverse 特定功能
  mattverse: {
    getWorkflows: () => ipcRenderer.invoke('mattverse:get-workflows'),
    saveWorkflow: (workflow: any) => ipcRenderer.invoke('mattverse:save-workflow', workflow),
    deleteWorkflow: (id: string) => ipcRenderer.invoke('mattverse:delete-workflow', id)
  }
}

// 使用 electron-core 的预加载桥接工厂
const api = createPreloadBridge(customAPI)

// 暴露 API 到渲染进程
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electronAPI', api)
    logger.info('Mattverse APIs exposed successfully')
  } catch (error) {
    logger.error('Failed to expose Mattverse APIs:', error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electronAPI = api
}

// 导出类型定义
export type ElectronAPI = typeof api

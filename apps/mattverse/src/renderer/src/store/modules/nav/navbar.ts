import { createPersistConfig } from '@/store/plugins/persist-config'
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

/**
 * 导航栏项目接口
 */
export interface NavbarItem {
  id: string
  title: string
  path: string
  icon?: string
  description?: string
  isActive?: boolean
  children?: NavbarItem[]
}

/**
 * 面包屑项目接口
 */
export interface BreadcrumbItem {
  title: string
  path?: string
  isActive?: boolean
}

/**
 * 工作流标签页接口
 */
export interface WorkflowTab {
  id: string
  name: string
  path: string
  isActive: boolean
  isDirty?: boolean // 是否有未保存的更改
}

/**
 * 自定义操作接口
 */
export interface CustomAction {
  id: string
  label: string
  icon?: string
  handler: () => void
  disabled?: boolean
  tooltip?: string
}

/**
 * 导航栏配置接口
 */
export interface NavbarConfig {
  showTitle: boolean
  showDescription: boolean
  showActions: boolean
  theme: 'neutral' | 'primary' | 'secondary'
}

/**
 * 导航栏状态管理
 */
export const useNavbarStore = defineStore(
  'navbar',
  () => {
    // 状态
    const currentPageTitle = ref<string>('工作流')
    const currentPageDescription = ref<string>('')
    const showBackButton = ref<boolean>(false)
    const customActions = ref<CustomAction[]>([])

    // 工作流标签页状态
    const workflowTabs = ref<WorkflowTab[]>([])
    const activeWorkflowTabId = ref<string>('')
    const workflowListVisited = ref<boolean>(false)

    // 导航栏配置
    const navbarConfig = ref<NavbarConfig>({
      showTitle: true,
      showDescription: true,
      showActions: true,
      theme: 'neutral',
    })

    // 计算属性 - 面包屑需要在组件中动态计算
    const breadcrumbItems = ref<BreadcrumbItem[]>([])

    // 路由标题映射
    const routeTitleMap: Record<string, string> = {
      '/workflow': '工作流',
      '/task': '计算任务',
      '/server': '服务器状态',
      '/logger': '日志',
      '/tools': '工具管理',
      '/tools/node': '节点工具模块',
      '/tools/agent': 'Agent工具模块',
      '/tools/other': '其它工具模块',
      '/setting': '设置',
      '/setting/basic': '基本设置',
      '/setting/middleware': '中台设置',
      '/setting/flow': '流程设置',
      '/setting/about': '关于我们',
    }

    // 生成面包屑的方法
    const generateBreadcrumbs = (
      routePath: string,
      routeMeta?: { title?: string }
    ): BreadcrumbItem[] => {
      const pathSegments = routePath.split('/').filter(Boolean)
      const items: BreadcrumbItem[] = []

      // 添加首页
      items.push({
        title: '首页',
        path: '/',
        isActive: false,
      })

      // 检查是否为工作流编辑器路径
      const isWorkflowEditor = routePath.includes('/workflow/editor/')

      // 根据路径生成面包屑
      let currentPath = ''
      pathSegments.forEach((segment, index) => {
        currentPath += `/${segment}`
        const isLast = index === pathSegments.length - 1

        // 如果是工作流编辑器，跳过"editor"和ID段
        if (isWorkflowEditor) {
          if (
            segment === 'editor' ||
            (index === pathSegments.length - 1 && formatSegmentTitle(segment).startsWith('ID:'))
          ) {
            return // 跳过这些段
          }
        }

        let title = segment

        // 优先级：路由标题映射 > 路由meta > 格式化标题
        if (routeTitleMap[currentPath]) {
          title = routeTitleMap[currentPath]
        } else if (isLast && routeMeta?.title) {
          title = routeMeta.title
        } else {
          title = formatSegmentTitle(segment)
        }

        items.push({
          title,
          path: isLast ? undefined : currentPath,
          isActive: isLast,
        })
      })

      return items
    }

    // 格式化路径段标题的辅助函数
    const formatSegmentTitle = (segment: string): string => {
      // 如果是纯数字或UUID格式，可能是ID
      if (/^[0-9a-f-]{8,}$/i.test(segment) || /^\d+$/.test(segment)) {
        return `ID: ${segment.slice(0, 8)}...`
      }

      // 首字母大写
      return segment.charAt(0).toUpperCase() + segment.slice(1)
    }

    // 更新面包屑
    const updateBreadcrumbs = (routePath: string, routeMeta?: { title?: string }) => {
      breadcrumbItems.value = generateBreadcrumbs(routePath, routeMeta)
    }

    // 工作流标签页管理方法
    const addWorkflowTab = (tab: Omit<WorkflowTab, 'isActive'>) => {
      // 检查是否已存在
      const existingTab = workflowTabs.value.find((t) => t.id === tab.id)
      if (existingTab) {
        // 如果已存在，激活它
        setActiveWorkflowTab(tab.id)
        return
      }

      // 将所有现有标签设为非活跃
      workflowTabs.value.forEach((t) => (t.isActive = false))

      // 添加新标签并设为活跃
      const newTab: WorkflowTab = {
        ...tab,
        isActive: true,
      }
      workflowTabs.value.push(newTab)
      activeWorkflowTabId.value = tab.id
    }

    const removeWorkflowTab = (tabId: string) => {
      const index = workflowTabs.value.findIndex((t) => t.id === tabId)
      if (index === -1) return

      const wasActive = workflowTabs.value[index].isActive
      workflowTabs.value.splice(index, 1)

      // 如果删除的是活跃标签，需要激活另一个
      if (wasActive && workflowTabs.value.length > 0) {
        // 优先激活右边的标签，如果没有则激活左边的
        const newActiveIndex = index < workflowTabs.value.length ? index : index - 1
        if (newActiveIndex >= 0) {
          setActiveWorkflowTab(workflowTabs.value[newActiveIndex].id)
        }
      } else if (workflowTabs.value.length === 0) {
        activeWorkflowTabId.value = ''
      }
    }

    const setActiveWorkflowTab = (tabId: string) => {
      workflowTabs.value.forEach((tab) => {
        tab.isActive = tab.id === tabId
      })
      activeWorkflowTabId.value = tabId
    }

    const updateWorkflowTab = (tabId: string, updates: Partial<WorkflowTab>) => {
      const tab = workflowTabs.value.find((t) => t.id === tabId)
      if (tab) {
        Object.assign(tab, updates)
      }
    }

    const clearWorkflowTabs = () => {
      workflowTabs.value = []
      activeWorkflowTabId.value = ''
    }

    const setWorkflowListVisited = (visited: boolean) => {
      workflowListVisited.value = visited
    }

    const clearActiveWorkflowTab = () => {
      workflowTabs.value.forEach((tab) => {
        tab.isActive = false
      })
      activeWorkflowTabId.value = ''
    }

    // 计算属性
    const activeWorkflowTab = computed(() => workflowTabs.value.find((tab) => tab.isActive))

    const isWorkflowRoute = computed(() => {
      return breadcrumbItems.value.some(
        (item) => item.title === '工作流' || item.path?.includes('/workflow')
      )
    })

    // 方法
    const setCurrentPage = (title: string, description?: string) => {
      currentPageTitle.value = title
      currentPageDescription.value = description || ''
    }

    const setShowBackButton = (show: boolean) => {
      showBackButton.value = show
    }

    const addCustomAction = (action: CustomAction) => {
      customActions.value.push(action)
    }

    const removeCustomAction = (actionId: string) => {
      const index = customActions.value.findIndex((action) => action.id === actionId)
      if (index > -1) {
        customActions.value.splice(index, 1)
      }
    }

    const clearCustomActions = () => {
      customActions.value = []
    }

    const updateNavbarConfig = (config: Partial<NavbarConfig>) => {
      navbarConfig.value = { ...navbarConfig.value, ...config }
    }

    // 重置到默认状态
    const resetToDefault = () => {
      currentPageTitle.value = '工作流'
      currentPageDescription.value = ''
      showBackButton.value = false
      customActions.value = []
      clearWorkflowTabs()
      navbarConfig.value = {
        showTitle: true,
        showDescription: true,
        showActions: true,
        theme: 'neutral',
      }
    }

    return {
      // 状态
      currentPageTitle,
      currentPageDescription,
      showBackButton,
      customActions,
      navbarConfig,
      breadcrumbItems,
      workflowTabs,
      activeWorkflowTabId,
      workflowListVisited,

      // 计算属性
      activeWorkflowTab,
      isWorkflowRoute,

      // 方法
      setCurrentPage,
      setShowBackButton,
      addCustomAction,
      removeCustomAction,
      clearCustomActions,
      updateNavbarConfig,
      updateBreadcrumbs,
      generateBreadcrumbs,

      // 工作流标签页方法
      addWorkflowTab,
      removeWorkflowTab,
      setActiveWorkflowTab,
      updateWorkflowTab,
      clearWorkflowTabs,
      setWorkflowListVisited,
      clearActiveWorkflowTab,

      resetToDefault,
    }
  },
  {
    persist: createPersistConfig('navbar'),
  }
)

/***
 * Pinia 持久化插件配置
 */
import type { PersistenceOptions } from 'pinia-plugin-persistedstate'

/**
 * 默认持久化配置
 */
export const defaultPersistConfig: PersistenceOptions = {
  storage: localStorage,
}

/**
 * 多个持久化配置
 */
export const persistConfigs = {
  //工作流相关
  workflow: {
    key: 'mattverse_workflow',
    storage: localStorage,
    paths: ['workflows', 'currentWorkflowId'],
  } as PersistenceOptions,
  //文件夹相关
  folder: {
    key: 'mattverse_folder',
    storage: localStorage,
    paths: ['folders', 'currentFolderId'],
  } as PersistenceOptions,
  //导航栏相关
  navbar: {
    key: 'mattverse_navbar',
    storage: localStorage,
    paths: [
      'workflowTabs',
      'activeWorkflowTabId',
      'workflowListVisited',
      'currentPageTitle',
      'currentPageDescription',
      'showBackButton',
      'customActions',
      'navbarConfig',
      'breadcrumbItems',
    ],
  } as PersistenceOptions,
}

/***
 * 创建持久化配置
 * @param storename 仓库名称
 * @param customConfig 自定义配置
 * @returns 持久化配置
 */
export function createPersistConfig(
  storename: keyof typeof persistConfigs,
  customConfig?: Partial<PersistenceOptions>
): PersistenceOptions {
  const baseConfig = persistConfigs[storename]
  return {
    ...defaultPersistConfig,
    ...baseConfig,
    ...customConfig,
  }
}

/**
 * 存储类型枚举
 * @see https://prazdevs.github.io/pinia-plugin-persistedstate/zh/
 */
export enum StorageType {
  LOCAL = 'local',
  SESSION = 'session',
  COOKIE = 'cookie',
  INDEXEDDB = 'indexeddb',
  MEMORY = 'memory',
}

/**
 * 根据环境选择存储类型
 * @param preferredType 首选存储类型
 * @returns 实际存储对象
 */
export function getStorage(preferredType: StorageType = StorageType.LOCAL): Storage {
  switch (preferredType) {
    case StorageType.LOCAL:
      return localStorage
    case StorageType.SESSION:
      return sessionStorage
    // case StorageType.COOKIE:
    //   return cookieStorage
    // case StorageType.INDEXEDDB:
    //   return indexedDBStorage
    // case StorageType.MEMORY:
    //   return memoryStorage
    default:
      return localStorage
  }
}

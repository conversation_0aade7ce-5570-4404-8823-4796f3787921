<template>
  <form>
    <SidebarGroup class="py-0">
      <SidebarGroupContent class="relative">
        <Label for="search" class="sr-only">搜索导航</Label>

        <!-- 简化的搜索输入框 -->
        <div class="relative">
          <input
            id="search"
            v-model="searchQuery"
            placeholder="搜索导航项... (Ctrl+K)"
            class="pl-8 pr-12 h-8 w-full border-0 bg-sidebar-accent/50 text-sidebar-foreground placeholder:text-sidebar-foreground/50 focus:bg-sidebar-accent focus:outline-none transition-colors rounded-md"
            @focus="isOpen = true"
            @blur="handleBlur"
          />
          <Search
            class="pointer-events-none absolute left-2 top-1/2 size-4 -translate-y-1/2 select-none opacity-50"
          />
          <!-- 键盘快捷键提示 -->
          <div
            class="absolute right-2 top-1/2 -translate-y-1/2 text-xs text-sidebar-foreground/50 group-data-[collapsible=icon]:hidden"
          >
            <kbd class="px-1.5 py-0.5 text-xs bg-sidebar-accent/30 rounded border">⌘K</kbd>
          </div>
        </div>

        <!-- 搜索结果下拉列表 -->
        <div
          v-if="
            isOpen && searchQuery && (filteredMainItems.length > 0 || filteredSubItems.length > 0)
          "
          class="absolute top-full left-0 right-0 mt-1 bg-popover border rounded-md shadow-lg z-50 max-h-[300px] overflow-y-auto"
        >
          <!-- 主导航项 -->
          <div v-if="filteredMainItems.length > 0" class="p-1">
            <div class="px-2 py-1.5 text-xs font-medium text-muted-foreground">主导航</div>
            <div
              v-for="item in filteredMainItems"
              :key="item.url"
              class="flex items-center gap-2 px-2 py-1.5 hover:bg-accent hover:text-accent-foreground rounded-sm cursor-pointer transition-colors"
              @click="handleItemSelect(item)"
            >
              <MattIcon :name="item.icon" v-if="item.icon" class="size-4 text-foreground/70" />
              <span class="flex-1" v-html="highlightMatch(item.title, searchQuery)"></span>
              <MattIcon name="ArrowRight" class="size-3 text-foreground/30" />
            </div>
          </div>

          <!-- 子导航项 -->
          <div v-if="filteredSubItems.length > 0" class="p-1">
            <div class="px-2 py-1.5 text-xs font-medium text-muted-foreground">子导航</div>
            <div
              v-for="item in filteredSubItems"
              :key="item.url"
              class="flex items-center gap-2 px-2 py-1.5 pl-6 hover:bg-accent hover:text-accent-foreground rounded-sm cursor-pointer transition-colors"
              @click="handleItemSelect(item)"
            >
              <MattIcon :name="item.icon" v-if="item.icon" class="size-4 text-foreground/70" />
              <div class="flex-1">
                <div v-html="highlightMatch(item.title, searchQuery)"></div>
                <div class="text-xs text-muted-foreground" v-if="item.parentTitle">
                  来自: {{ item.parentTitle }}
                </div>
              </div>
              <MattIcon name="ArrowRight" class="size-3 text-foreground/30" />
            </div>
          </div>
        </div>

        <!-- 无结果提示 -->
        <div
          v-if="
            isOpen && searchQuery && filteredMainItems.length === 0 && filteredSubItems.length === 0
          "
          class="absolute top-full left-0 right-0 mt-1 bg-popover border rounded-md shadow-lg z-50 p-4 text-center text-sm text-muted-foreground"
        >
          未找到匹配的导航项
        </div>
      </SidebarGroupContent>
    </SidebarGroup>
  </form>
</template>

<script setup lang="ts">
import { computed, ref, inject, onMounted, onUnmounted, type ComputedRef } from 'vue'
import { useRouter } from 'vue-router'

// 定义导航项类型
interface NavItem {
  title: string
  url: string
  icon?: string
  isActive?: boolean
  items?: {
    title: string
    icon?: string
    url: string
  }[]
}

interface SearchableNavItem {
  title: string
  url: string
  icon?: string
  parentTitle?: string
}

// 注入导航数据（从父组件传递）
const navItems = inject<ComputedRef<NavItem[]>>(
  'navItems',
  computed(() => [])
)
const router = useRouter()

// 搜索状态
const searchQuery = ref('')
const selectedItem = ref<SearchableNavItem | null>(null)
const isOpen = ref(false)

// 将导航数据扁平化为可搜索的项目
const searchableItems = computed(() => {
  const items: SearchableNavItem[] = []

  navItems.value.forEach((navItem: NavItem) => {
    // 添加主导航项
    items.push({
      title: navItem.title,
      url: navItem.url,
      icon: navItem.icon,
    })

    // 添加子导航项
    if (navItem.items) {
      navItem.items.forEach((subItem) => {
        items.push({
          title: subItem.title,
          url: subItem.url,
          icon: subItem.icon,
          parentTitle: navItem.title,
        })
      })
    }
  })

  return items
})

// 过滤主导航项
const filteredMainItems = computed(() => {
  if (!searchQuery.value) return []

  const query = searchQuery.value.toLowerCase()
  return searchableItems.value.filter(
    (item) => !item.parentTitle && item.title.toLowerCase().includes(query)
  )
})

// 过滤子导航项
const filteredSubItems = computed(() => {
  if (!searchQuery.value) return []

  const query = searchQuery.value.toLowerCase()
  return searchableItems.value.filter(
    (item) =>
      item.parentTitle &&
      (item.title.toLowerCase().includes(query) || item.parentTitle.toLowerCase().includes(query))
  )
})

// 高亮匹配文本
const highlightMatch = (text: string, query: string): string => {
  if (!query) return text

  const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
  return text.replace(regex, '<mark class="bg-yellow-200 text-yellow-900 px-0.5 rounded">$1</mark>')
}

// 处理失去焦点
const handleBlur = () => {
  // 延迟关闭，允许点击搜索结果
  setTimeout(() => {
    isOpen.value = false
  }, 200)
}

// 处理项目选择
const handleItemSelect = (item: SearchableNavItem | null) => {
  if (item) {
    // 导航到选中的页面
    router.push(item.url)
    // 清空搜索
    searchQuery.value = ''
    selectedItem.value = null
    isOpen.value = false

    // 记录搜索日志（如果 logger 存在）
    if (typeof window !== 'undefined' && 'logger' in window) {
      ;(window as any).logger?.info('导航搜索', {
        title: item.title,
        url: item.url,
        parentTitle: item.parentTitle,
      })
    }
  }
}

// 键盘快捷键支持
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl/Cmd + K 打开搜索
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault()
    const searchInput = document.getElementById('search') as HTMLInputElement
    if (searchInput) {
      searchInput.focus()
      isOpen.value = true
    }
  }

  // Escape 关闭搜索
  if (event.key === 'Escape') {
    searchQuery.value = ''
    selectedItem.value = null
    isOpen.value = false
    const searchInput = document.getElementById('search') as HTMLInputElement
    if (searchInput) {
      searchInput.blur()
    }
  }
}

// 添加键盘事件监听
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

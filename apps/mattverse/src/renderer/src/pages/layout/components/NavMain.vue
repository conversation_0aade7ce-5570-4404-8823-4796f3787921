<template>
  <SidebarGroup>
    <SidebarMenu>
      <template v-for="item in items" :key="item.title">
        <!-- 有子项的导航项 -->
        <Collapsible
          v-if="
            item.items &&
            item.items.filter((subItem) => subItem.meta?.showInMenu !== false).length > 0
          "
          as-child
          :default-open="false"
          class="group/collapsible"
        >
          <SidebarMenuItem class="cursor-pointer">
            <!-- 折叠状态下的 DropdownMenu -->
            <DropdownMenu v-if="isSidebarCollapsed">
              <DropdownMenuTrigger as-child>
                <SidebarMenuButton
                  :tooltip="item.title"
                  :class="getMenuButtonClass(item)"
                  @click="handleParentNavigation(item)"
                >
                  <MattIcon :name="item.icon" v-if="item.icon" />
                  <span>{{ item.title }}</span>
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent side="right" align="start" class="w-48 border-none">
                <DropdownMenuLabel>{{ item.title }}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  v-for="subItem in item.items.filter(
                    (subItem) => subItem.meta?.showInMenu !== false
                  )"
                  :key="subItem.title"
                  @click="handleNavigation(subItem.url)"
                  :class="getSubMenuItemClass(subItem)"
                >
                  <MattIcon :name="subItem.icon" v-if="subItem.icon" class="mr-2 h-4 w-4" />
                  {{ subItem.title }}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <!-- 展开状态下的 Collapsible -->
            <template v-else>
              <CollapsibleTrigger as-child>
                <SidebarMenuButton
                  :tooltip="item.title"
                  :class="getMenuButtonClass(item)"
                  @click="handleParentNavigation(item)"
                >
                  <MattIcon :name="item.icon" v-if="item.icon" />
                  <span>{{ item.title }}</span>
                  <MattIcon
                    name="ChevronRight"
                    class="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"
                  />
                </SidebarMenuButton>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <SidebarMenuSub>
                  <SidebarMenuSubItem
                    v-for="subItem in item.items.filter(
                      (subItem) => subItem.meta?.showInMenu !== false
                    )"
                    :key="subItem.title"
                  >
                    <SidebarMenuSubButton as-child>
                      <div
                        :class="getSubMenuButtonClass(subItem)"
                        @click="handleNavigation(subItem.url)"
                      >
                        <MattIcon :name="subItem.icon" v-if="subItem.icon" />
                        <span>{{ subItem.title }}</span>
                      </div>
                    </SidebarMenuSubButton>
                  </SidebarMenuSubItem>
                </SidebarMenuSub>
              </CollapsibleContent>
            </template>
          </SidebarMenuItem>
        </Collapsible>

        <!-- 没有子项的导航项 -->
        <SidebarMenuItem v-else class="cursor-pointer">
          <SidebarMenuButton
            :tooltip="item.title"
            :class="getMenuButtonClass(item)"
            @click="handleParentNavigation(item)"
          >
            <MattIcon :name="item.icon" v-if="item.icon" />
            <span>{{ item.title }}</span>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </template>
    </SidebarMenu>
  </SidebarGroup>
</template>
<script setup lang="ts">
import { useNavbar } from '@/composables'
import { useNavbarStore } from '@/store'

defineProps<{
  items: {
    title: string
    url: string
    icon?: string
    isActive?: boolean
    items?: {
      title: string
      icon?: string
      url: string
      meta?: {
        showInMenu?: boolean
      }
    }[]
  }[]
}>()

const router = useRouter()
const route = useRoute()
const { state } = useSidebar()
const navbar = useNavbar()
const navbarStore = useNavbarStore()

// 判断侧边栏是否折叠
const isSidebarCollapsed = computed(() => state.value === 'collapsed')

// 判断当前路由是否匹配指定路径
const isRouteActive = (url: string) => {
  return route.path === url || route.path.startsWith(url + '/')
}

// 判断父级菜单是否有活跃的子项
const hasActiveChild = (item: any) => {
  if (!item.items) return false
  return item.items.some((subItem: any) => isRouteActive(subItem.url))
}

// 获取主菜单按钮的样式类
const getMenuButtonClass = (item: any) => {
  const isActive = isRouteActive(item.url) || hasActiveChild(item)
  return {
    'bg-sidebar-accent text-sidebar-accent-foreground': isActive,
    'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground': !isActive,
  }
}

// 获取子菜单项的样式类（DropdownMenu 中使用）
const getSubMenuItemClass = (subItem: any) => {
  const isActive = isRouteActive(subItem.url)
  return {
    'bg-accent text-accent-foreground': isActive,
  }
}

// 获取子菜单按钮的样式类（Collapsible 中使用）
const getSubMenuButtonClass = (subItem: any) => {
  const isActive = isRouteActive(subItem.url)
  return {
    'flex justify-start items-center cursor-pointer': true,
    'bg-sidebar-accent text-sidebar-accent-foreground': isActive,
    'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground': !isActive,
  }
}

const handleNavigation = (url: string) => {
  // 特殊处理工作流导航：只有当前不在工作流页面且有活跃标签页时，才跳转到标签页
  if (url === '/workflow' && navbarStore.activeWorkflowTab && !route.path.startsWith('/workflow')) {
    const activeTab = navbarStore.activeWorkflowTab
    // 重置工作流列表访问标记，允许下次自动跳转
    navbarStore.setWorkflowListVisited(false)
    router.push(activeTab.path)
    return
  }

  router.push(url)
  // 导航后会自动触发路由变化，useNavbar的watchRouteChanges会自动更新面包屑
}

// 处理父级导航点击
const handleParentNavigation = (item: any) => {
  // 特殊处理工作流导航：只有当前不在工作流页面且有活跃标签页时，才跳转到标签页
  if (
    item.url === '/workflow' &&
    navbarStore.activeWorkflowTab &&
    !route.path.startsWith('/workflow')
  ) {
    const activeTab = navbarStore.activeWorkflowTab
    // 重置工作流列表访问标记，允许下次自动跳转
    navbarStore.setWorkflowListVisited(false)
    router.push(activeTab.path)
    return
  }

  // 如果有子项，导航到父路由（会触发重定向到默认子路由）
  if (item.items && item.items.length > 0) {
    router.push(item.url)
  } else {
    // 如果没有子项，直接导航
    router.push(item.url)
  }
  // 导航后会自动触发路由变化，useNavbar的watchRouteChanges会自动更新面包屑
}
</script>

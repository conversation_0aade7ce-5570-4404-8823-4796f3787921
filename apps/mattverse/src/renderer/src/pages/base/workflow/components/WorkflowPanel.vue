<template>
  <div class="h-full flex flex-col">
    <!-- 搜索和导入区域 -->
    <div class="flex items-center justify-between gap-3 p-3 border-b border-border">
      <div class="relative w-full max-w-sm items-center">
        <Input v-model="searchKeyword" placeholder="搜索..." class="!pl-10" @input="handleSearch" />
        <span
          class="absolute start-0 inset-y-0 flex items-center justify-center px-3 text-muted-foreground pointer-events-none"
        >
          <MattIcon name="Search" class="size-4" />
        </span>
      </div>
      <Button @click="handleImport">
        <MattIcon name="Upload" class="mr-2 h-4 w-4" />
        导入
      </Button>
    </div>

    <!-- 工作流列表区域 -->
    <div class="flex-1">
      <div v-if="currentWorkflows.length > 0">
        <div
          class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 p-2 overflow-y-auto overscroll-x-none max-h-[calc(100vh-8rem)] scrollbar"
        >
          <Card
            v-for="workflow in currentWorkflows"
            :key="workflow.id"
            class="group cursor-pointer transition-all duration-200 hover:shadow-md flex h-45 flex-col border-none"
            @click="handleOpenWorkflow(workflow)"
          >
            <CardContent class="p-2 flex flex-col h-full justify-between">
              <!-- 头部：图标和操作菜单 -->
              <div class="flex items-start justify-between mb-1">
                <div class="p-2 rounded bg-muted">
                  <MattIcon name="Workflow" class="h-4 w-4 text-muted-foreground" />
                </div>

                <!-- 操作菜单 -->
                <DropdownMenu>
                  <DropdownMenuTrigger as-child>
                    <Button
                      variant="ghost"
                      size="sm"
                      class="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                      @click.stop
                    >
                      <MattIcon name="MoreHorizontal" class="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" class="border-none">
                    <DropdownMenuItem @click="handleEditWorkflow(workflow)">
                      <MattIcon name="Edit2" class="mr-2 h-4 w-4" />
                      重命名
                    </DropdownMenuItem>
                    <DropdownMenuItem @click="handleDuplicateWorkflow(workflow.id)">
                      <MattIcon name="Copy" class="mr-2 h-4 w-4" />
                      复制
                    </DropdownMenuItem>
                    <DropdownMenuItem @click="handleExportWorkflow(workflow.id)">
                      <MattIcon name="Download" class="mr-2 h-4 w-4" />
                      导出
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      class="text-destructive focus:text-destructive"
                      @click="handleDeleteWorkflow(workflow)"
                    >
                      <MattIcon name="Trash2" class="mr-2 h-4 w-4 text-destructive" />
                      删除
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              <!-- 标题 -->
              <h3 class="font-semibold text-base mb-2 truncate">{{ workflow.title }}</h3>

              <!-- 描述 -->
              <p class="text-xs text-muted-foreground mb-2 line-clamp-2 flex-1">
                {{ workflow.description || '暂无描述' }}
              </p>

              <!-- 时间 -->
              <div class="flex items-center text-xs text-muted-foreground">
                <MattIcon name="Clock" class="mr-1 h-3 w-3" />
                {{ MattverseShared.formatDate(workflow.updateTime) }}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="h-full flex items-center justify-center">
        <MattEmptyState
          icon="Workflow"
          :icon-size="48"
          title="暂无工作流"
          :description="
            searchKeyword
              ? '没有找到匹配的工作流'
              : '当前文件夹中没有工作流，您可以创建一个新的工作流或导入已有的工作流'
          "
        >
          <template #action>
            <Button @click="handleCreateWorkflow">
              <MattIcon name="Plus" class="mr-2 h-4 w-4" />
              创建工作流
            </Button>
            <Button variant="outline" @click="handleImport">
              <MattIcon name="Upload" class="mr-2 h-4 w-4" />
              导入工作流
            </Button>
          </template>
        </MattEmptyState>
      </div>
    </div>

    <!-- 工作流对话框 -->
    <Dialog v-model:open="workflowDialogOpen">
      <DialogContent class="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {{ workflowDialogMode === 'create' ? '添加工作流' : '编辑工作流' }}
          </DialogTitle>
        </DialogHeader>
        <div class="space-y-4 py-4">
          <div class="space-y-2">
            <Label for="workflow-title">名称</Label>
            <Input
              id="workflow-title"
              v-model="workflowForm.title"
              placeholder="请输入名称"
              @keyup.enter="handleConfirmWorkflow"
            />
          </div>
          <div class="space-y-2">
            <Label for="workflow-description">描述</Label>
            <Textarea
              id="workflow-description"
              v-model="workflowForm.description"
              placeholder="请输入描述"
              rows="3"
              class="resize-none"
            />
            <div class="text-xs text-muted-foreground text-right">
              {{ workflowForm.description.length }}/350
            </div>
          </div>
        </div>
        <DialogFooter class="gap-2">
          <Button variant="outline" @click="workflowDialogOpen = false" class="flex-1">
            取消
          </Button>
          <Button
            @click="handleConfirmWorkflow"
            :disabled="!workflowForm.title.trim()"
            class="flex-1 bg-black text-white hover:bg-black/90"
          >
            {{ workflowDialogMode === 'create' ? '添加' : '保存' }}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- 删除确认对话框 -->
    <AlertDialog v-model:open="deleteDialogOpen">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认删除</AlertDialogTitle>
          <AlertDialogDescription>
            您确定要删除工作流 "{{ workflowToDelete?.title }}" 吗？此操作无法撤销。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel @click="deleteDialogOpen = false"> 取消 </AlertDialogCancel>
          <AlertDialogAction
            class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            @click="confirmDeleteWorkflow"
          >
            删除
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useWorkflowStore, useFolderStore, useNavbarStore, type WorkflowItem } from '@/store'
import * as MattverseShared from '@mattverse/shared'
import { storeToRefs } from 'pinia'
import { toast } from 'vue-sonner'
import { useDebounceFn } from '@vueuse/core'
import { useRouter } from 'vue-router'

// Router
const router = useRouter()

// Store
const workflowStore = useWorkflowStore()
const folderStore = useFolderStore()
const navbarStore = useNavbarStore()
const { currentFolderId } = storeToRefs(folderStore)
const { searchKeyword } = storeToRefs(workflowStore)

// 计算属性
const currentWorkflows = computed(() =>
  workflowStore.getCurrentFolderWorkflows(currentFolderId.value)
)

// 对话框状态
const workflowDialogOpen = ref(false)
const workflowDialogMode = ref<'create' | 'edit'>('create')
const deleteDialogOpen = ref(false)

// 表单数据
const workflowForm = ref({
  id: '',
  title: '',
  description: '',
})

const workflowToDelete = ref<WorkflowItem | null>(null)

// 事件定义
const emit = defineEmits<{
  openWorkflow: [workflow: WorkflowItem]
}>()

const handleSearch = useDebounceFn(() => {
  // 搜索逻辑已在 store 中处理
}, 300)

const handleCreateWorkflow = () => {
  workflowDialogMode.value = 'create'
  workflowForm.value = {
    id: '',
    title: '',
    description: '',
  }
  workflowDialogOpen.value = true
}

const handleEditWorkflow = (workflow: WorkflowItem) => {
  workflowDialogMode.value = 'edit'
  workflowForm.value = {
    id: workflow.id,
    title: workflow.title,
    description: workflow.description,
  }
  workflowDialogOpen.value = true
}

const handleDuplicateWorkflow = (workflowId: string) => {
  const newWorkflow = workflowStore.duplicateWorkflow(workflowId)
  if (newWorkflow) {
    toast.success('工作流复制成功', {
      description: `已创建工作流 "${newWorkflow.title}"`,
    })
  }
}

const handleDeleteWorkflow = (workflow: WorkflowItem) => {
  workflowToDelete.value = workflow
  deleteDialogOpen.value = true
}

const handleOpenWorkflow = (workflow: WorkflowItem) => {
  // 导航到工作流编辑器
  router.push({
    name: 'workflow-editor',
    params: { id: workflow.id },
    query: { title: workflow.title },
  })
  emit('openWorkflow', workflow)
}

const handleConfirmWorkflow = () => {
  const title = workflowForm.value.title.trim()
  if (!title) {
    toast.error('请输入工作流名称')
    return
  }

  if (workflowDialogMode.value === 'create') {
    const newWorkflow = workflowStore.createWorkflow({
      title,
      description: workflowForm.value.description,
      folderId: currentFolderId.value,
    })
    toast.success('工作流创建成功', {
      description: `已创建工作流 "${newWorkflow.title}"`,
    })
    // 创建工作流时不自动打开页签，只有点击工作流时才打开
  } else {
    const success = workflowStore.updateWorkflow({
      id: workflowForm.value.id,
      title,
      description: workflowForm.value.description,
    })
    if (success) {
      toast.success('工作流更新成功')
    } else {
      toast.error('工作流更新失败')
    }
  }

  workflowDialogOpen.value = false
}

const confirmDeleteWorkflow = () => {
  if (workflowToDelete.value) {
    const workflowId = workflowToDelete.value.id
    const workflowTitle = workflowToDelete.value.title

    const success = workflowStore.deleteWorkflow(workflowId)
    if (success) {
      // 同步删除对应的页签
      navbarStore.removeWorkflowTab(workflowId)

      toast.success('工作流删除成功', {
        description: `已删除工作流 "${workflowTitle}"`,
      })
    } else {
      toast.error('工作流删除失败')
    }
  }
  deleteDialogOpen.value = false
  workflowToDelete.value = null
}

const handleImport = () => {
  // TODO: 实现导入功能
  toast.info('导入功能开发中...')
}

const handleExportWorkflow = (_workflowId: string) => {
  // TODO: 实现导出功能
  toast.info('导出功能开发中...')
}

// 暴露给父组件的方法
const openCreateDialog = () => {
  handleCreateWorkflow()
}

// 暴露方法给父组件
defineExpose({
  openCreateDialog,
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>

<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <Button variant="ghost" size="sm" class="h-8 w-8 px-0">
        <span class="text-lg">{{ currentLocaleInfo.flag }}</span>
        <span class="sr-only">{{ $t('components.language_selector.title') }}</span>
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end" class="w-48">
      <DropdownMenuLabel>{{ $t('components.language_selector.title') }}</DropdownMenuLabel>
      <DropdownMenuSeparator />
      <DropdownMenuItem
        v-for="locale in SUPPORTED_LOCALES"
        :key="locale.code"
        @click="handleLanguageChange(locale.code)"
        :class="{ 'bg-accent': locale.code === currentLocale }"
      >
        <span class="mr-2">{{ locale.flag }}</span>
        <span class="flex-1">{{ locale.name }}</span>
        <Check v-if="locale.code === currentLocale" class="h-4 w-4" />
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Check } from 'lucide-vue-next'
import { SUPPORTED_LOCALES, type SupportedLocale } from '@mattverse/i18n'
import { switchLanguage, getCurrentLocale } from '@/i18n'

// 获取当前语言
const currentLocale = computed(() => getCurrentLocale())

// 获取当前语言信息
const currentLocaleInfo = computed(() => {
  return SUPPORTED_LOCALES.find(locale => locale.code === currentLocale.value) || SUPPORTED_LOCALES[0]
})

// 处理语言切换
const handleLanguageChange = (locale: SupportedLocale) => {
  if (locale !== currentLocale.value) {
    switchLanguage(locale)
    
    // 可选：显示切换成功提示
    // toast.success(`语言已切换为 ${SUPPORTED_LOCALES.find(l => l.code === locale)?.name}`)
  }
}
</script>

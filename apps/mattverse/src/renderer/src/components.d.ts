/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AlertDialog: typeof import('@mattverse/mattverse-ui')['AlertDialog']
    AlertDialogAction: typeof import('@mattverse/mattverse-ui')['AlertDialogAction']
    AlertDialogCancel: typeof import('@mattverse/mattverse-ui')['AlertDialogCancel']
    AlertDialogContent: typeof import('@mattverse/mattverse-ui')['AlertDialogContent']
    AlertDialogDescription: typeof import('@mattverse/mattverse-ui')['AlertDialogDescription']
    AlertDialogFooter: typeof import('@mattverse/mattverse-ui')['AlertDialogFooter']
    AlertDialogHeader: typeof import('@mattverse/mattverse-ui')['AlertDialogHeader']
    AlertDialogTitle: typeof import('@mattverse/mattverse-ui')['AlertDialogTitle']
    Avatar: typeof import('@mattverse/mattverse-ui')['Avatar']
    AvatarFallback: typeof import('@mattverse/mattverse-ui')['AvatarFallback']
    AvatarImage: typeof import('@mattverse/mattverse-ui')['AvatarImage']
    Button: typeof import('@mattverse/mattverse-ui')['Button']
    Card: typeof import('@mattverse/mattverse-ui')['Card']
    CardContent: typeof import('@mattverse/mattverse-ui')['CardContent']
    Collapsible: typeof import('@mattverse/mattverse-ui')['Collapsible']
    CollapsibleContent: typeof import('@mattverse/mattverse-ui')['CollapsibleContent']
    CollapsibleTrigger: typeof import('@mattverse/mattverse-ui')['CollapsibleTrigger']
    ContextMenu: typeof import('@mattverse/mattverse-ui')['ContextMenu']
    ContextMenuContent: typeof import('@mattverse/mattverse-ui')['ContextMenuContent']
    ContextMenuItem: typeof import('@mattverse/mattverse-ui')['ContextMenuItem']
    ContextMenuSeparator: typeof import('@mattverse/mattverse-ui')['ContextMenuSeparator']
    ContextMenuTrigger: typeof import('@mattverse/mattverse-ui')['ContextMenuTrigger']
    Dialog: typeof import('@mattverse/mattverse-ui')['Dialog']
    DialogContent: typeof import('@mattverse/mattverse-ui')['DialogContent']
    DialogFooter: typeof import('@mattverse/mattverse-ui')['DialogFooter']
    DialogHeader: typeof import('@mattverse/mattverse-ui')['DialogHeader']
    DialogTitle: typeof import('@mattverse/mattverse-ui')['DialogTitle']
    DropdownMenu: typeof import('@mattverse/mattverse-ui')['DropdownMenu']
    DropdownMenuContent: typeof import('@mattverse/mattverse-ui')['DropdownMenuContent']
    DropdownMenuGroup: typeof import('@mattverse/mattverse-ui')['DropdownMenuGroup']
    DropdownMenuItem: typeof import('@mattverse/mattverse-ui')['DropdownMenuItem']
    DropdownMenuLabel: typeof import('@mattverse/mattverse-ui')['DropdownMenuLabel']
    DropdownMenuSeparator: typeof import('@mattverse/mattverse-ui')['DropdownMenuSeparator']
    DropdownMenuTrigger: typeof import('@mattverse/mattverse-ui')['DropdownMenuTrigger']
    Input: typeof import('@mattverse/mattverse-ui')['Input']
    Label: typeof import('@mattverse/mattverse-ui')['Label']
    MattEmptyState: typeof import('@mattverse/mattverse-ui')['MattEmptyState']
    MattIcon: typeof import('@mattverse/mattverse-ui')['MattIcon']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Search: typeof import('lucide-vue-next')['Search']
    Separator: typeof import('@mattverse/mattverse-ui')['Separator']
    Sidebar: typeof import('@mattverse/mattverse-ui')['Sidebar']
    SidebarContent: typeof import('@mattverse/mattverse-ui')['SidebarContent']
    SidebarFooter: typeof import('@mattverse/mattverse-ui')['SidebarFooter']
    SidebarGroup: typeof import('@mattverse/mattverse-ui')['SidebarGroup']
    SidebarGroupContent: typeof import('@mattverse/mattverse-ui')['SidebarGroupContent']
    SidebarHeader: typeof import('@mattverse/mattverse-ui')['SidebarHeader']
    SidebarInset: typeof import('@mattverse/mattverse-ui')['SidebarInset']
    SidebarMenu: typeof import('@mattverse/mattverse-ui')['SidebarMenu']
    SidebarMenuButton: typeof import('@mattverse/mattverse-ui')['SidebarMenuButton']
    SidebarMenuItem: typeof import('@mattverse/mattverse-ui')['SidebarMenuItem']
    SidebarMenuSub: typeof import('@mattverse/mattverse-ui')['SidebarMenuSub']
    SidebarMenuSubButton: typeof import('@mattverse/mattverse-ui')['SidebarMenuSubButton']
    SidebarMenuSubItem: typeof import('@mattverse/mattverse-ui')['SidebarMenuSubItem']
    SidebarProvider: typeof import('@mattverse/mattverse-ui')['SidebarProvider']
    SidebarRail: typeof import('@mattverse/mattverse-ui')['SidebarRail']
    SidebarTrigger: typeof import('@mattverse/mattverse-ui')['SidebarTrigger']
    Textarea: typeof import('@mattverse/mattverse-ui')['Textarea']
  }
}

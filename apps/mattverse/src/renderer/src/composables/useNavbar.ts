import { useNavbarStore } from '@/store/modules/nav'
import { useRoute, useRouter } from 'vue-router'
import { watch, onMounted, onUnmounted } from 'vue'

/**
 * Navbar 管理 composable
 * 提供便捷的方法来管理导航栏状态
 */
export function useNavbar() {
  const navbarStore = useNavbarStore()
  const route = useRoute()
  const router = useRouter()

  /**
   * 设置当前页面信息
   */
  const setPageInfo = (title: string, description?: string) => {
    navbarStore.setCurrentPage(title, description)
  }

  /**
   * 显示返回按钮
   */
  const showBackButton = () => {
    navbarStore.setShowBackButton(true)
  }

  /**
   * 隐藏返回按钮
   */
  const hideBackButton = () => {
    navbarStore.setShowBackButton(false)
  }

  /**
   * 添加自定义操作按钮
   */
  const addAction = (action: {
    id: string
    label: string
    icon?: string
    variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
    size?: 'default' | 'sm' | 'lg' | 'icon'
    onClick: () => void
  }) => {
    navbarStore.addCustomAction(action)
  }

  /**
   * 移除自定义操作按钮
   */
  const removeAction = (actionId: string) => {
    navbarStore.removeCustomAction(actionId)
  }

  /**
   * 清空所有自定义操作按钮
   */
  const clearActions = () => {
    navbarStore.clearCustomActions()
  }

  /**
   * 更新导航栏配置
   */
  const updateConfig = (config: {
    showTitle?: boolean
    showDescription?: boolean
    showActions?: boolean
    theme?: 'neutral' | 'primary' | 'secondary'
  }) => {
    navbarStore.updateNavbarConfig(config)
  }

  /**
   * 根据路由自动设置页面标题
   */
  const autoSetPageTitle = () => {
    const routeMeta = route.meta
    if (routeMeta?.title) {
      setPageInfo(routeMeta.title as string, routeMeta.description as string)
    }
  }

  /**
   * 监听路由变化，自动更新页面信息
   */
  const watchRouteChanges = () => {
    return watch(
      [() => route.path, () => route.meta],
      ([newPath, newMeta]) => {
        // 更新面包屑，传递router实例以获取路由配置
        navbarStore.updateBreadcrumbs(newPath, newMeta, router)
        // 清空自定义操作
        clearActions()
        // 隐藏返回按钮
        hideBackButton()
        // 自动设置页面标题
        autoSetPageTitle()
      },
      { immediate: true }
    )
  }

  /**
   * 导航到指定路径
   */
  const navigateTo = (path: string) => {
    router.push(path)
  }

  /**
   * 返回上一页
   */
  const goBack = () => {
    router.back()
  }

  /**
   * 获取当前面包屑路径
   */
  const getBreadcrumbs = () => {
    return navbarStore.breadcrumbItems
  }

  /**
   * 添加工作流标签页
   */
  const addWorkflowTab = (id: string, name: string, path?: string) => {
    const tabPath = path || `/workflow/editor/${id}`
    navbarStore.addWorkflowTab({
      id,
      name,
      path: tabPath,
      isDirty: false
    })
  }

  /**
   * 移除工作流标签页
   */
  const removeWorkflowTab = (id: string) => {
    navbarStore.removeWorkflowTab(id)
  }

  /**
   * 设置工作流标签页为脏状态（有未保存更改）
   */
  const setWorkflowTabDirty = (id: string, isDirty: boolean = true) => {
    navbarStore.updateWorkflowTab(id, { isDirty })
  }

  /**
   * 切换到指定工作流标签页
   */
  const switchToWorkflowTab = (id: string) => {
    navbarStore.setActiveWorkflowTab(id)
    const tab = navbarStore.workflowTabs.find((t: any) => t.id === id)
    if (tab) {
      router.push(tab.path)
    }
  }

  return {
    // Store 实例
    navbarStore,

    // 页面信息管理
    setPageInfo,
    autoSetPageTitle,

    // 返回按钮管理
    showBackButton,
    hideBackButton,

    // 自定义操作管理
    addAction,
    removeAction,
    clearActions,

    // 配置管理
    updateConfig,

    // 路由管理
    watchRouteChanges,
    navigateTo,
    goBack,

    // 面包屑
    getBreadcrumbs,

    // 工作流标签页管理
    addWorkflowTab,
    removeWorkflowTab,
    setWorkflowTabDirty,
    switchToWorkflowTab
  }
}

/**
 * 页面级别的 navbar 管理 hook
 * 在页面组件中使用，自动管理生命周期
 */
export function usePageNavbar(options?: {
  title?: string
  description?: string
  showBackButton?: boolean
  autoWatch?: boolean
}) {
  const navbar = useNavbar()
  let unwatchRoute: (() => void) | null = null

  onMounted(() => {
    // 设置页面信息
    if (options?.title) {
      navbar.setPageInfo(options.title, options?.description)
    } else {
      navbar.autoSetPageTitle()
    }

    // 设置返回按钮
    if (options?.showBackButton) {
      navbar.showBackButton()
    }

    // 监听路由变化
    if (options?.autoWatch !== false) {
      unwatchRoute = navbar.watchRouteChanges()
    }
  })

  onUnmounted(() => {
    // 清理
    navbar.clearActions()
    navbar.hideBackButton()
    
    // 停止监听路由变化
    if (unwatchRoute) {
      unwatchRoute()
    }
  })

  return navbar
}

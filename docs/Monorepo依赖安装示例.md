# Monorepo 依赖管理手册

## 目录

- [项目架构概述](#项目架构概述)
- [依赖安装命令](#依赖安装命令)
- [工作空间管理](#工作空间管理)
- [依赖层级关系](#依赖层级关系)
- [常用场景示例](#常用场景示例)
- [依赖管理最佳实践](#依赖管理最佳实践)
- [故障排除](#故障排除)

## 项目架构概述

### 包结构

```
mattverse-monorepo/
├── apps/                          # 应用程序
│   ├── mattverse/                 # 主应用
│   └── highpower/                 # 高性能应用
└── packages/                      # 共享包
    ├── configs/                   # 配置包
    ├── shared/                    # 共享工具库
    ├── electron-core/             # Electron 核心
    ├── mattverse-ui/              # UI 组件库
    └── mattverse-flow/            # 工作流组件
```

### 依赖关系图

```mermaid
graph TD
    A[apps/mattverse] --> B[packages/shared]
    A --> C[packages/electron-core]
    A --> D[packages/mattverse-ui]
    A --> E[packages/mattverse-flow]
    A --> F[packages/configs]

    G[apps/highpower] --> B
    G --> C
    G --> D
    G --> E
    G --> F

    C --> B
    D --> B
    E --> B

    F -.-> H[构建配置]
    B -.-> I[基础工具]
```

### 详细依赖层级图

```mermaid
flowchart TB
    subgraph "应用层 (Application Layer)"
        APP1[apps/mattverse<br/>主应用]
        APP2[apps/highpower<br/>高性能应用]
    end

    subgraph "核心层 (Core Layer)"
        CORE[packages/electron-core<br/>Electron 核心功能]
        UI[packages/mattverse-ui<br/>UI 组件库]
        FLOW[packages/mattverse-flow<br/>工作流组件]
    end

    subgraph "基础层 (Base Layer)"
        SHARED[packages/shared<br/>共享工具库]
        CONFIGS[packages/configs<br/>配置包]
    end

    APP1 --> CORE
    APP1 --> UI
    APP1 --> FLOW
    APP1 --> SHARED
    APP1 --> CONFIGS

    APP2 --> CORE
    APP2 --> UI
    APP2 --> FLOW
    APP2 --> SHARED
    APP2 --> CONFIGS

    CORE --> SHARED
    UI --> SHARED
    FLOW --> SHARED

    style APP1 fill:#e1f5fe
    style APP2 fill:#e1f5fe
    style CORE fill:#f3e5f5
    style UI fill:#f3e5f5
    style FLOW fill:#f3e5f5
    style SHARED fill:#e8f5e8
    style CONFIGS fill:#e8f5e8
```

### 构建依赖顺序图

```mermaid
graph LR
    subgraph "第一阶段 (基础层)"
        S[packages/shared]
        C[packages/configs]
    end

    subgraph "第二阶段 (核心层)"
        EC[packages/electron-core]
        UI[packages/mattverse-ui]
        FL[packages/mattverse-flow]
    end

    subgraph "第三阶段 (应用层)"
        M[apps/mattverse]
        H[apps/highpower]
    end

    S --> EC
    S --> UI
    S --> FL

    EC --> M
    UI --> M
    FL --> M
    C --> M

    EC --> H
    UI --> H
    FL --> H
    C --> H

    style S fill:#4caf50
    style C fill:#4caf50
    style EC fill:#ff9800
    style UI fill:#ff9800
    style FL fill:#ff9800
    style M fill:#2196f3
    style H fill:#2196f3
```

## 依赖安装命令

### 全局依赖管理

```bash
# 安装所有依赖（推荐使用）
pnpm install

# 清理并重新安装所有依赖
pnpm clean:deps

# 清理构建缓存
pnpm clean:all
```

### 为特定包安装依赖

#### 应用级别

```bash
# 为 mattverse 应用安装依赖
pnpm add <package-name> --filter=mattverse

# 为 highpower 应用安装依赖
pnpm add <package-name> --filter=highpower

# 安装开发依赖
pnpm add -D <package-name> --filter=mattverse

# 安装 peer 依赖
pnpm add -P <package-name> --filter=mattverse
```

#### 包级别

```bash
# 为 shared 包安装依赖
pnpm add <package-name> --filter=@mattverse/shared

# 为 UI 组件库安装依赖
pnpm add <package-name> --filter=@mattverse/mattverse-ui

# 为工作流组件安装依赖
pnpm add <package-name> --filter=@mattverse/mattverse-flow

# 为 electron-core 安装依赖
pnpm add <package-name> --filter=@mattverse/electron-core

# 为 configs 包安装依赖
pnpm add <package-name> --filter=@mattverse/configs
```

### 批量操作

```bash
# 为所有应用安装相同依赖
pnpm add <package-name> --filter="./apps/*"

# 为所有包安装相同依赖
pnpm add <package-name> --filter="./packages/*"

# 为所有工作空间安装相同依赖
pnpm add <package-name> -r

# 为所有工作空间安装开发依赖
pnpm add -D <package-name> -r
```

## 工作空间管理

### 内部包依赖

```bash
# 添加内部包依赖（推荐方式）
pnpm add @mattverse/shared --filter=mattverse --workspace

# 添加特定版本的内部包
pnpm add @mattverse/shared@workspace:* --filter=mattverse

# 移除内部包依赖
pnpm remove @mattverse/shared --filter=mattverse
```

### 工作空间配置

项目使用 `pnpm-workspace.yaml` 配置工作空间：

```yaml
packages:
  - 'apps/*'
  - 'packages/*'
```

## 依赖层级关系

### 基础层（无依赖）

- `packages/configs` - 配置包，不依赖其他内部包
- `packages/shared` - 基础工具库，不依赖其他内部包

### 核心层（依赖基础层）

- `packages/electron-core` - 依赖 `@mattverse/shared`
- `packages/mattverse-ui` - 依赖 `@mattverse/shared`
- `packages/mattverse-flow` - 依赖 `@mattverse/shared`

### 应用层（依赖所有包）

- `apps/mattverse` - 依赖所有 packages
- `apps/highpower` - 依赖所有 packages

### 依赖安装顺序

```bash
# 1. 先构建基础包
pnpm build:shared

# 2. 再构建核心包
pnpm build:core
pnpm build:ui
pnpm build:flow

# 3. 最后构建应用
pnpm build:mattverse
pnpm build:highpower
```

## 常用场景示例

### Vue 生态系统

```bash
# 为应用添加 Vue Router
pnpm add vue-router@4 --filter=mattverse

# 为应用添加 Pinia
pnpm add pinia --filter=mattverse

# 为 UI 组件库添加 Vue 相关依赖
pnpm add @vueuse/core --filter=@mattverse/mattverse-ui
```

### Electron 相关

```bash
# 为应用添加 Electron 构建依赖
pnpm add -D electron --filter=mattverse
pnpm add -D electron-builder --filter=mattverse

# 为 electron-core 添加主进程依赖
pnpm add electron-log --filter=@mattverse/electron-core
pnpm add electron-store --filter=@mattverse/electron-core
```

### 开发工具

```bash
# 为所有包添加 TypeScript
pnpm add -D typescript --filter="./packages/*"

# 为所有工作空间添加 ESLint
pnpm add -D eslint --filter="*"

# 为特定包添加构建工具
pnpm add -D vite --filter=@mattverse/mattverse-ui
pnpm add -D tsup --filter=@mattverse/shared
```

### UI 和样式

```bash
# 为应用添加 Tailwind CSS
pnpm add -D tailwindcss --filter=mattverse

# 为 UI 组件库添加 Radix Vue
pnpm add @radix-vue/core --filter=@mattverse/mattverse-ui

# 为应用添加图标库
pnpm add lucide-vue-next --filter=mattverse
```

## 依赖管理最佳实践

### 1. 依赖分类原则

#### 生产依赖 (dependencies)
- 运行时必需的包
- 会被打包到最终产物中

```bash
pnpm add vue --filter=mattverse
pnpm add axios --filter=@mattverse/shared
```

#### 开发依赖 (devDependencies)
- 仅开发时需要的包
- 构建工具、类型定义、测试工具等

```bash
pnpm add -D typescript --filter=mattverse
pnpm add -D vite --filter=@mattverse/mattverse-ui
```

#### 对等依赖 (peerDependencies)
- 由使用方提供的依赖
- 通常用于库包

```bash
pnpm add -P vue --filter=@mattverse/mattverse-ui
```

### 2. 版本管理策略

#### 使用精确版本
```bash
# 推荐：使用精确版本
pnpm add vue@3.5.0 --filter=mattverse

# 避免：使用宽泛版本范围
pnpm add vue@^3.0.0 --filter=mattverse
```

#### 内部包版本
```bash
# 使用 workspace 协议
pnpm add @mattverse/shared@workspace:* --filter=mattverse
```

### 3. 依赖更新策略

```bash
# 检查过时的依赖
pnpm outdated

# 更新特定包的依赖
pnpm update --filter=mattverse

# 更新所有工作空间的依赖
pnpm update -r

# 交互式更新
pnpm update -i
```

### 4. 依赖去重

```bash
# 去除重复依赖
pnpm dedupe

# 检查依赖树
pnpm list --depth=2

# 查看特定包的依赖
pnpm list --filter=mattverse
```

## 故障排除

### 常见问题

#### 1. 依赖冲突

**症状**: 不同包需要同一依赖的不同版本

**解决方案**:
```bash
# 清理并重新安装
pnpm clean:deps

# 或手动清理
rm -rf node_modules pnpm-lock.yaml
pnpm install
```

#### 2. 内部包找不到

**症状**: 导入内部包时报错 "Module not found"

**解决方案**:
```bash
# 确保内部包已构建
pnpm build:shared

# 检查依赖是否正确添加
pnpm list --filter=mattverse | grep @mattverse

# 重新添加内部包依赖
pnpm add @mattverse/shared --filter=mattverse --workspace
```

#### 3. 类型定义缺失

**症状**: TypeScript 报错找不到类型定义

**解决方案**:
```bash
# 添加类型定义包
pnpm add -D @types/node --filter=mattverse

# 确保 tsconfig.json 配置正确
# 检查 types 字段和 moduleResolution
```

#### 4. 构建失败

**症状**: 构建时报错依赖缺失

**解决方案**:
```bash
# 按依赖顺序构建
pnpm build:packages
pnpm build:apps

# 或使用 Turbo 自动处理依赖顺序
pnpm build
```

### 调试命令

```bash
# 查看 pnpm 配置
pnpm config list

# 查看工作空间信息
pnpm list -r --depth=0

# 查看依赖树
pnpm list --depth=2

# 检查包的安装位置
pnpm list <package-name>

# 查看 lockfile 信息
pnpm audit

# 验证工作空间配置
pnpm -r exec pwd
```

### 性能优化

#### 1. 使用 .npmrc 配置

```bash
# 在根目录创建 .npmrc
echo "shamefully-hoist=true" >> .npmrc
echo "strict-peer-dependencies=false" >> .npmrc
```

#### 2. 缓存管理

```bash
# 清理 pnpm 缓存
pnpm store prune

# 查看缓存大小
pnpm store path
```

#### 3. 并行安装

```bash
# 使用并行安装（默认启用）
pnpm install --parallel

# 限制并发数
pnpm install --parallel --max-concurrent=4
```

## 总结

遵循本手册的依赖管理策略，可以确保：

- **依赖隔离**: 每个包有明确的依赖边界
- **代码复用**: 通过工作空间协议共享内部包
- **版本一致**: pnpm 确保相同依赖版本的复用
- **构建效率**: Turbo 缓存机制提升构建速度
- **类型安全**: TypeScript 跨包类型检查
- **维护性**: 清晰的依赖关系便于维护和升级


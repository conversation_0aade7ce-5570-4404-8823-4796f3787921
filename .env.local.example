# 本地环境配置示例文件
# 复制此文件为 .env.local 并根据需要修改配置

# API 配置
VITE_APP_LINKER_HOST=***********
VITE_APP_LINKER_PORT=29998
# 登录用户信息
VITE_APP_USER_ID=0
VITE_APP_USER_TOKEN=abcdefghijklmn


# 本地开发配置
VITE_APP_ENV=local
NODE_ENV=development

# 调试配置
VITE_APP_DEBUG=true
VITE_APP_LOG_LEVEL=debug
VITE_APP_CONSOLE_LOG=true

# 本地服务器配置
VITE_PORT=3000
VITE_HOST=0.0.0.0
VITE_OPEN=true

# 开发工具配置
VITE_APP_DEVTOOLS=true
VITE_APP_MOCK_API=false



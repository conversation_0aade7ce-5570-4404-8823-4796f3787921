#!/usr/bin/env node
/**
 * 交互式脚本启动器
 * 提供统一的脚本选择和执行界面
 */

import { spawn } from 'child_process'
import { createInterface } from 'readline'
import { existsSync } from 'fs'
import { join } from 'path'

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// 可用脚本配置
const SCRIPT_CATEGORIES = {
  构建脚本: {
    '跨平台构建 (Node.js)': {
      path: 'scripts/build/build-all-platforms.js',
      command: 'node',
      description: '支持并发构建多个应用的多个平台版本',
    },
    '批量构建 (Linux/macOS)': {
      path: 'scripts/build/build-all.sh',
      command: 'bash',
      description: 'Linux/macOS 批量构建脚本',
      condition: () => process.platform !== 'win32',
    },
    '批量构建 (Windows)': {
      path: 'scripts/build/build-all.ps1',
      command: 'powershell',
      description: 'Windows 批量构建脚本',
      condition: () => process.platform === 'win32',
    },
  },
  开发工具: {
    开发环境管理器: {
      path: 'scripts/dev/dev-manager.js',
      command: 'node',
      description: '交互式多应用开发服务器管理',
    },
  },
  维护工具: {
    项目健康检查: {
      path: 'scripts/maintenance/health-check.js',
      command: 'node',
      description: '全面检查项目配置和状态',
    },
    '批量清理 (Windows)': {
      path: 'scripts/maintenance/clean-all.ps1',
      command: 'powershell',
      description: '支持多种清理类型的批量清理脚本',
      condition: () => process.platform === 'win32',
    },
    '批量清理 (Linux/macOS)': {
      path: 'scripts/maintenance/clean-all.sh',
      command: 'bash',
      description: '支持多种清理类型的批量清理脚本',
      condition: () => process.platform !== 'win32',
    },
  },
  信息工具: {
    项目信息查看: {
      path: 'scripts/utils/project-info.js',
      command: 'node',
      description: '显示项目结构、依赖关系、构建状态等',
    },
  },
  代码生成器: {
    创建新应用: {
      path: 'scripts/generators/create-app.js',
      command: 'node',
      description: '交互式创建新的 Electron 应用',
    },
    创建新包: {
      path: 'scripts/generators/create-package.js',
      command: 'node',
      description: '交互式创建新的可复用包',
    },
  },
}

class InteractiveLauncher {
  constructor() {
    this.rl = createInterface({
      input: process.stdin,
      output: process.stdout,
    })
    this.rootPath = process.cwd()
  }

  async showMainMenu() {
    console.clear()
    log('🚀 MattVerse 交互式脚本启动器', 'bright')
    log('='.repeat(50), 'cyan')

    const categories = Object.keys(SCRIPT_CATEGORIES)
    categories.forEach((category, index) => {
      log(`${index + 1}) ${category}`, 'green')
    })
    log(`${categories.length + 1}) 退出`, 'red')
    log()
  }

  async showCategoryMenu(categoryName) {
    console.clear()
    log(`📂 ${categoryName}`, 'bright')
    log('='.repeat(50), 'cyan')

    const scripts = SCRIPT_CATEGORIES[categoryName]
    const availableScripts = []

    Object.entries(scripts).forEach(([name, config]) => {
      // 检查条件和文件存在性
      if (config.condition && !config.condition()) return
      if (!existsSync(join(this.rootPath, config.path))) return

      availableScripts.push({ name, config })
    })

    availableScripts.forEach((script, index) => {
      log(`${index + 1}) ${script.name}`, 'green')
      log(`   ${script.config.description}`, 'blue')
      log('') // 添加空行分隔
    })

    log(`${availableScripts.length + 1}) 返回主菜单`, 'yellow')
    log(`${availableScripts.length + 2}) 退出`, 'red')
    log('')

    return availableScripts
  }

  async askChoice(max) {
    return new Promise((resolve) => {
      const ask = () => {
        this.rl.question(`请选择 (1-${max}): `, (answer) => {
          const choice = parseInt(answer.trim())
          if (isNaN(choice) || choice < 1 || choice > max) {
            log('无效选项，请重新输入', 'red')
            ask()
          } else {
            resolve(choice)
          }
        })
      }
      ask()
    })
  }

  async confirmExecution(scriptName, scriptPath) {
    log(`\n📋 即将执行脚本:`, 'yellow')
    log(`  名称: ${scriptName}`, 'green')
    log(`  路径: ${scriptPath}`, 'blue')

    // 检查是否是交互式脚本
    const isInteractiveScript = scriptPath.includes('dev-manager') ||
                               scriptPath.includes('generators/')

    if (isInteractiveScript) {
      log(`  类型: 交互式脚本`, 'cyan')
      log(`  注意: 此脚本将接管终端控制`, 'yellow')
    }

    log('')

    return new Promise((resolve) => {
      const ask = () => {
        this.rl.question('确认执行? (y/n): ', (answer) => {
          const choice = answer.trim().toLowerCase()
          if (choice === 'y' || choice === 'yes') {
            resolve(true)
          } else if (choice === 'n' || choice === 'no') {
            resolve(false)
          } else {
            log('请输入 y 或 n', 'red')
            ask()
          }
        })
      }
      ask()
    })
  }

  async executeScript(scriptConfig) {
    const scriptPath = join(this.rootPath, scriptConfig.path)

    log(`\n🚀 启动脚本: ${scriptConfig.path}`, 'cyan')
    log('='.repeat(50), 'cyan')

    // 检查是否是交互式脚本
    const isInteractiveScript =
      scriptConfig.path.includes('dev-manager') ||
      scriptConfig.path.includes('interactive-launcher') ||
      scriptConfig.path.includes('generators/')

    if (isInteractiveScript) {
      // 对于交互式脚本，完全关闭当前的 readline 接口
      this.rl.close()
      log('正在启动交互式脚本，请稍候...', 'cyan')
    }

    return new Promise((resolve) => {
      const child = spawn(scriptConfig.command, [scriptPath], {
        stdio: 'inherit',
        shell: true,
        cwd: this.rootPath,
      })

      child.on('close', (code) => {
        if (isInteractiveScript) {
          // 对于交互式脚本，直接退出启动器
          log('\n交互式脚本已结束', 'cyan')
          process.exit(code)
        }

        log('\n='.repeat(50), 'cyan')
        if (code === 0) {
          log('✅ 脚本执行完成', 'green')
        } else {
          log(`❌ 脚本执行失败，退出码: ${code}`, 'red')
        }

        // 等待用户按键继续
        this.rl.question('\n按 Enter 键继续...', () => {
          resolve(code)
        })
      })

      child.on('error', (error) => {
        if (isInteractiveScript) {
          log(`❌ 交互式脚本执行出错: ${error.message}`, 'red')
          process.exit(1)
        }

        log(`❌ 脚本执行出错: ${error.message}`, 'red')
        this.rl.question('\n按 Enter 键继续...', () => {
          resolve(1)
        })
      })
    })
  }

  async run() {
    while (true) {
      // 显示主菜单
      await this.showMainMenu()

      const categories = Object.keys(SCRIPT_CATEGORIES)
      const mainChoice = await this.askChoice(categories.length + 1)

      // 处理退出
      if (mainChoice === categories.length + 1) {
        log('👋 再见！', 'yellow')
        this.rl.close()
        process.exit(0)
      }

      // 显示分类菜单
      const categoryName = categories[mainChoice - 1]
      const availableScripts = await this.showCategoryMenu(categoryName)

      if (availableScripts.length === 0) {
        log('该分类下没有可用的脚本', 'yellow')
        this.rl.question('按 Enter 键继续...', () => {})
        continue
      }

      const categoryChoice = await this.askChoice(availableScripts.length + 2)

      // 处理返回主菜单
      if (categoryChoice === availableScripts.length + 1) {
        continue
      }

      // 处理退出
      if (categoryChoice === availableScripts.length + 2) {
        log('👋 再见！', 'yellow')
        this.rl.close()
        process.exit(0)
      }

      // 执行选中的脚本
      const selectedScript = availableScripts[categoryChoice - 1]

      if (await this.confirmExecution(selectedScript.name, selectedScript.config.path)) {
        await this.executeScript(selectedScript.config)
      } else {
        log('已取消执行', 'yellow')
        this.rl.question('按 Enter 键继续...', () => {})
      }
    }
  }
}

// 处理退出信号
process.on('SIGINT', () => {
  console.log('\n👋 再见！')
  process.exit(0)
})

// 启动交互式启动器
const launcher = new InteractiveLauncher()
launcher.run().catch((error) => {
  console.error('❌ 启动器运行出错:', error.message)
  process.exit(1)
})

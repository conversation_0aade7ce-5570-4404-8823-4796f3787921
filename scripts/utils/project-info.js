#!/usr/bin/env node
/**
 * 项目信息查看工具
 * 显示项目结构、依赖关系、构建状态等信息
 */

import { readFileSync, existsSync, readdirSync, statSync } from 'fs'
import { join } from 'path'

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

class ProjectInfo {
  constructor() {
    this.rootPath = process.cwd()
  }

  getPackageInfo(packagePath) {
    const packageJsonPath = join(packagePath, 'package.json')
    if (!existsSync(packageJsonPath)) {
      return null
    }

    try {
      const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'))
      return {
        name: packageJson.name,
        version: packageJson.version,
        description: packageJson.description,
        dependencies: Object.keys(packageJson.dependencies || {}),
        devDependencies: Object.keys(packageJson.devDependencies || {}),
        scripts: Object.keys(packageJson.scripts || {}),
        hasDistFolder: existsSync(join(packagePath, 'dist')),
        hasOutFolder: existsSync(join(packagePath, 'out')),
        hasReleaseFolder: existsSync(join(packagePath, 'release'))
      }
    } catch (error) {
      return { error: error.message }
    }
  }

  getDirectorySize(dirPath) {
    if (!existsSync(dirPath)) return 0
    
    let size = 0
    try {
      const files = readdirSync(dirPath)
      for (const file of files) {
        const filePath = join(dirPath, file)
        const stats = statSync(filePath)
        if (stats.isDirectory()) {
          size += this.getDirectorySize(filePath)
        } else {
          size += stats.size
        }
      }
    } catch (error) {
      // 忽略权限错误等
    }
    return size
  }

  formatSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB']
    let size = bytes
    let unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`
  }

  showProjectOverview() {
    log('\n📊 项目概览', 'cyan')
    log('='.repeat(60), 'cyan')

    // 根目录信息
    const rootPackage = this.getPackageInfo(this.rootPath)
    if (rootPackage) {
      log(`项目名称: ${rootPackage.name}`, 'bright')
      log(`版本: ${rootPackage.version}`, 'green')
      if (rootPackage.description) {
        log(`描述: ${rootPackage.description}`, 'blue')
      }
    }

    // 工作空间统计
    const appsDir = join(this.rootPath, 'apps')
    const packagesDir = join(this.rootPath, 'packages')
    
    let appCount = 0
    let packageCount = 0
    
    if (existsSync(appsDir)) {
      appCount = readdirSync(appsDir).filter(item => {
        const itemPath = join(appsDir, item)
        return statSync(itemPath).isDirectory() && existsSync(join(itemPath, 'package.json'))
      }).length
    }
    
    if (existsSync(packagesDir)) {
      packageCount = readdirSync(packagesDir).filter(item => {
        const itemPath = join(packagesDir, item)
        return statSync(itemPath).isDirectory() && existsSync(join(itemPath, 'package.json'))
      }).length
    }

    log(`应用数量: ${appCount}`, 'yellow')
    log(`包数量: ${packageCount}`, 'yellow')
    
    // 目录大小
    const nodeModulesSize = this.getDirectorySize(join(this.rootPath, 'node_modules'))
    if (nodeModulesSize > 0) {
      log(`node_modules 大小: ${this.formatSize(nodeModulesSize)}`, 'magenta')
    }
  }

  showAppsInfo() {
    log('\n🚀 应用信息', 'cyan')
    log('='.repeat(60), 'cyan')

    const appsDir = join(this.rootPath, 'apps')
    if (!existsSync(appsDir)) {
      log('未找到 apps 目录', 'red')
      return
    }

    const apps = readdirSync(appsDir).filter(item => {
      const itemPath = join(appsDir, item)
      return statSync(itemPath).isDirectory()
    })

    apps.forEach(app => {
      const appPath = join(appsDir, app)
      const info = this.getPackageInfo(appPath)
      
      log(`\n📱 ${app}`, 'bright')
      if (info) {
        if (info.error) {
          log(`  ❌ 错误: ${info.error}`, 'red')
        } else {
          log(`  名称: ${info.name}`, 'green')
          log(`  版本: ${info.version}`, 'blue')
          log(`  脚本数量: ${info.scripts.length}`, 'yellow')
          log(`  依赖数量: ${info.dependencies.length}`, 'yellow')
          log(`  开发依赖数量: ${info.devDependencies.length}`, 'yellow')
          
          // 构建状态
          const buildStatus = []
          if (info.hasDistFolder) buildStatus.push('dist')
          if (info.hasOutFolder) buildStatus.push('out')
          if (info.hasReleaseFolder) buildStatus.push('release')
          
          if (buildStatus.length > 0) {
            log(`  构建产物: ${buildStatus.join(', ')}`, 'green')
          } else {
            log(`  构建产物: 无`, 'red')
          }
        }
      } else {
        log(`  ❌ 未找到 package.json`, 'red')
      }
    })
  }

  showPackagesInfo() {
    log('\n📦 包信息', 'cyan')
    log('='.repeat(60), 'cyan')

    const packagesDir = join(this.rootPath, 'packages')
    if (!existsSync(packagesDir)) {
      log('未找到 packages 目录', 'red')
      return
    }

    const packages = readdirSync(packagesDir).filter(item => {
      const itemPath = join(packagesDir, item)
      return statSync(itemPath).isDirectory()
    })

    packages.forEach(pkg => {
      const pkgPath = join(packagesDir, pkg)
      const info = this.getPackageInfo(pkgPath)
      
      log(`\n📚 ${pkg}`, 'bright')
      if (info) {
        if (info.error) {
          log(`  ❌ 错误: ${info.error}`, 'red')
        } else {
          log(`  名称: ${info.name}`, 'green')
          log(`  版本: ${info.version}`, 'blue')
          log(`  脚本数量: ${info.scripts.length}`, 'yellow')
          log(`  依赖数量: ${info.dependencies.length}`, 'yellow')
          log(`  开发依赖数量: ${info.devDependencies.length}`, 'yellow')
          
          // 构建状态
          if (info.hasDistFolder) {
            log(`  构建状态: ✅ 已构建`, 'green')
          } else {
            log(`  构建状态: ❌ 未构建`, 'red')
          }
        }
      } else {
        log(`  ❌ 未找到 package.json`, 'red')
      }
    })
  }

  showScriptsInfo() {
    log('\n⚙️  可用脚本', 'cyan')
    log('='.repeat(60), 'cyan')

    const rootPackage = this.getPackageInfo(this.rootPath)
    if (!rootPackage || !rootPackage.scripts) {
      log('未找到脚本配置', 'red')
      return
    }

    // 按类别分组脚本
    const scriptCategories = {
      '基础工具': ['prepare', 'format', 'commit', 'setup', 'health-check'],
      '构建命令': ['build', 'build:apps', 'build:packages', 'build:shared', 'build:ui', 'build:flow', 'build:core', 'build:configs'],
      '应用构建': ['build:mattverse', 'build:highpower'],
      '平台构建': ['build:mattverse:win', 'build:mattverse:mac', 'build:mattverse:linux', 'build:highpower:win', 'build:highpower:mac', 'build:highpower:linux', 'build:all-platforms'],
      '开发命令': ['dev', 'dev:mattverse', 'dev:highpower', 'dev:ui', 'dev:flow', 'dev:manager'],
      '预览命令': ['preview', 'preview:mattverse', 'preview:highpower'],
      '代码质量': ['lint', 'lint:fix', 'typecheck'],
      '测试命令': ['test', 'test:watch'],
      '清理命令': ['clean', 'clean:all', 'clean:deps', 'clean:build'],
      '发布管理': ['changeset', 'changeset:version', 'changeset:publish', 'version', 'release']
    }

    Object.entries(scriptCategories).forEach(([category, scripts]) => {
      const availableScripts = scripts.filter(script => rootPackage.scripts.includes(script))
      if (availableScripts.length > 0) {
        log(`\n${category}:`, 'bright')
        availableScripts.forEach(script => {
          log(`  pnpm ${script}`, 'green')
        })
      }
    })
  }

  run() {
    const args = process.argv.slice(2)
    const command = args[0] || 'overview'

    switch (command) {
      case 'overview':
        this.showProjectOverview()
        break
      case 'apps':
        this.showAppsInfo()
        break
      case 'packages':
        this.showPackagesInfo()
        break
      case 'scripts':
        this.showScriptsInfo()
        break
      case 'all':
        this.showProjectOverview()
        this.showAppsInfo()
        this.showPackagesInfo()
        this.showScriptsInfo()
        break
      default:
        log('用法: node scripts/utils/project-info.js [command]', 'yellow')
        log('命令:', 'cyan')
        log('  overview  - 项目概览 (默认)', 'green')
        log('  apps      - 应用信息', 'green')
        log('  packages  - 包信息', 'green')
        log('  scripts   - 可用脚本', 'green')
        log('  all       - 显示所有信息', 'green')
    }
  }
}

// 运行工具
const projectInfo = new ProjectInfo()
projectInfo.run()

#!/usr/bin/env node

/**
 * 创建新包脚本 - 支持交互式和命令行模式
 * 使用方法: 
 *   交互式: node scripts/generators/create-package.js
 *   命令行: node scripts/generators/create-package.js <package-name> [description]
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import readline from 'readline'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 创建交互式界面
function createInterface() {
  return readline.createInterface({
    input: process.stdin,
    output: process.stdout,
    terminal: true
  })
}

// 询问用户输入
function askQuestion(rl, question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim())
    })
  })
}

// 替换模板变量
function replaceVariables(content, variables) {
  let result = content
  for (const [key, value] of Object.entries(variables)) {
    const regex = new RegExp(`{{${key}}}`, 'g')
    result = result.replace(regex, value)
  }
  return result
}

// 复制目录
function copyDirectory(src, dest, variables) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true })
  }

  const files = fs.readdirSync(src)
  
  for (const file of files) {
    const srcPath = path.join(src, file)
    const destPath = path.join(dest, file)
    
    const stat = fs.statSync(srcPath)
    
    if (stat.isDirectory()) {
      copyDirectory(srcPath, destPath, variables)
    } else {
      let content = fs.readFileSync(srcPath, 'utf-8')
      content = replaceVariables(content, variables)
      fs.writeFileSync(destPath, content)
    }
  }
}

// 验证包名称
function validatePackageName(name) {
  if (!name) {
    return '包名称不能为空'
  }
  
  if (!/^[a-z0-9-]+$/.test(name)) {
    return '包名称只能包含小写字母、数字和连字符'
  }
  
  if (name.length < 2) {
    return '包名称至少需要2个字符'
  }
  
  return null
}

// 交互式模式
async function interactiveMode() {
  const rl = createInterface()
  
  console.log('📦 Mattverse 包创建器')
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
  console.log('')
  
  let packageName = ''
  let description = ''
  
  // 获取包名称
  while (true) {
    packageName = await askQuestion(rl, '📝 请输入包名称 (例如: my-utils): ')
    const error = validatePackageName(packageName)
    if (error) {
      console.log(`❌ ${error}`)
      continue
    }
    
    // 检查是否已存在
    const packageDir = path.resolve(process.cwd(), 'packages', packageName)
    if (fs.existsSync(packageDir)) {
      console.log(`❌ 包 ${packageName} 已存在`)
      continue
    }
    
    break
  }
  
  // 获取描述
  description = await askQuestion(rl, '📄 请输入包描述 (可选): ')
  if (!description) {
    description = `${packageName} - Mattverse 包`
  }
  
  console.log('')
  console.log('📋 包信息确认:')
  console.log(`   名称: @mattverse/${packageName}`)
  console.log(`   描述: ${description}`)
  console.log(`   路径: packages/${packageName}`)
  console.log('')
  
  const confirm = await askQuestion(rl, '✅ 确认创建? (y/N): ')
  
  rl.close()
  
  if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
    console.log('❌ 已取消创建')
    return
  }
  
  return { packageName, description }
}

// 创建包
function createPackage(packageName, description) {
  console.log(`📦 正在创建包: @mattverse/${packageName}`)
  
  const packageDir = path.resolve(process.cwd(), 'packages', packageName)
  const templateDir = path.resolve(__dirname, '..', '..', 'templates', 'package-template')
  
  if (!fs.existsSync(templateDir)) {
    console.error('❌ 包模板不存在')
    process.exit(1)
  }
  
  const variables = {
    PACKAGE_NAME: packageName,
    DESCRIPTION: description,
  }
  
  copyDirectory(templateDir, packageDir, variables)
  
  console.log('')
  console.log('✅ 包创建成功!')
  console.log(`📁 包目录: packages/${packageName}`)
  console.log('')
  console.log('🔧 下一步操作:')
  console.log(`   cd packages/${packageName}`)
  console.log(`   pnpm install`)
  console.log(`   pnpm build`)
  console.log('')
}

// 主函数
async function main() {
  const args = process.argv.slice(2)
  
  if (args.length === 0) {
    // 交互式模式
    const result = await interactiveMode()
    if (result) {
      createPackage(result.packageName, result.description)
    }
  } else {
    // 命令行模式
    const packageName = args[0]
    const description = args[1] || `${packageName} - Mattverse 包`
    
    const error = validatePackageName(packageName)
    if (error) {
      console.error(`❌ ${error}`)
      process.exit(1)
    }
    
    const packageDir = path.resolve(process.cwd(), 'packages', packageName)
    if (fs.existsSync(packageDir)) {
      console.error(`❌ 包 ${packageName} 已存在`)
      process.exit(1)
    }
    
    createPackage(packageName, description)
  }
}

main().catch(console.error)

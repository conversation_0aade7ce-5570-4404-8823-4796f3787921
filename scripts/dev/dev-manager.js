#!/usr/bin/env node
/**
 * Development Environment Manager
 * Supports starting multiple application development servers with unified management interface
 */

import { spawn } from 'child_process'
import { createInterface } from 'readline'

// Configuration
const APPS = {
  mattverse: {
    name: '<PERSON><PERSON><PERSON>',
    command: 'pnpm',
    args: ['dev:mattverse'],
    port: 5173,
    color: '\x1b[36m', // cyan
  },
  highpower: {
    name: 'HighPower',
    command: 'pnpm',
    args: ['dev:highpower'],
    port: 5174,
    color: '\x1b[35m', // magenta
  },
  ui: {
    name: 'UI Components',
    command: 'pnpm',
    args: ['dev:ui'],
    port: 5175,
    color: '\x1b[33m', // yellow
  },
  flow: {
    name: 'Flow Components',
    command: 'pnpm',
    args: ['dev:flow'],
    port: 5176,
    color: '\x1b[32m', // green
  },
}

// Colors and styles
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
}

class DevManager {
  constructor() {
    this.processes = new Map()
    this.rl = createInterface({
      input: process.stdin,
      output: process.stdout,
    })
  }

  log(message, color = 'reset') {
    const timestamp = new Date().toLocaleTimeString()
    console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`)
  }

  logApp(appKey, message) {
    const app = APPS[appKey]
    const timestamp = new Date().toLocaleTimeString()
    console.log(`${app.color}[${timestamp}] [${app.name}] ${message}${colors.reset}`)
  }

  async startApp(appKey) {
    if (this.processes.has(appKey)) {
      this.log(`${APPS[appKey].name} 已在运行中`, 'yellow')
      return
    }

    const app = APPS[appKey]
    this.log(`正在启动 ${app.name}...`, 'blue')

    const child = spawn(app.command, app.args, {
      stdio: 'inherit',
      shell: true,
      cwd: process.cwd(),
    })

    this.processes.set(appKey, {
      process: child,
      app: app,
      startTime: Date.now(),
    })

    child.on('close', (code) => {
      this.processes.delete(appKey)
      if (code === 0) {
        this.log(`${app.name} 正常退出`, 'green')
      } else {
        this.log(`${app.name} 异常退出，代码: ${code}`, 'red')
      }
    })

    child.on('error', (error) => {
      this.processes.delete(appKey)
      this.log(`${app.name} 错误: ${error.message}`, 'red')
    })

    await new Promise((resolve) => setTimeout(resolve, 1000))
    this.log(`${app.name} 启动成功`, 'green')
  }

  stopApp(appKey) {
    const processInfo = this.processes.get(appKey)
    if (!processInfo) {
      this.log(`${APPS[appKey].name} 未在运行`, 'yellow')
      return
    }

    this.log(`正在停止 ${processInfo.app.name}...`, 'blue')
    processInfo.process.kill('SIGTERM')

    // 强制终止（如果需要）
    setTimeout(() => {
      if (this.processes.has(appKey)) {
        processInfo.process.kill('SIGKILL')
      }
    }, 5000)
  }

  stopAll() {
    this.log('正在停止所有应用...', 'blue')
    for (const appKey of this.processes.keys()) {
      this.stopApp(appKey)
    }
  }

  showStatus() {
    console.log('\n' + colors.cyan + '='.repeat(60) + colors.reset)
    console.log(colors.bright + '开发服务器状态' + colors.reset)
    console.log(colors.cyan + '='.repeat(60) + colors.reset)

    for (const [appKey, app] of Object.entries(APPS)) {
      const processInfo = this.processes.get(appKey)
      const status = processInfo ? '🟢 运行中' : '🔴 已停止'
      const uptime = processInfo
        ? `(运行时间: ${Math.floor((Date.now() - processInfo.startTime) / 1000)}秒)`
        : ''

      console.log(`${app.color}${app.name.padEnd(20)} ${status} ${uptime}${colors.reset}`)
    }
    console.log()
  }

  showHelp() {
    console.log('\n' + colors.cyan + '='.repeat(60) + colors.reset)
    console.log(colors.bright + '可用命令' + colors.reset)
    console.log(colors.cyan + '='.repeat(60) + colors.reset)
    console.log('start <app>    - 启动指定应用 (mattverse, highpower, ui, flow)')
    console.log('stop <app>     - 停止指定应用')
    console.log('restart <app>  - 重启指定应用')
    console.log('start-all      - 启动所有应用')
    console.log('stop-all       - 停止所有应用')
    console.log('status         - 显示状态')
    console.log('menu           - 显示交互菜单')
    console.log('create-app     - 创建新应用')
    console.log('create-package - 创建新包')
    console.log('help           - 显示帮助')
    console.log('quit/exit      - 退出管理器')
    console.log()
  }

  showMenu() {
    console.log('\n' + colors.cyan + '='.repeat(60) + colors.reset)
    console.log(colors.bright + '快速操作菜单' + colors.reset)
    console.log(colors.cyan + '='.repeat(60) + colors.reset)
    console.log('1) 启动 MattVerse')
    console.log('2) 启动 HighPower')
    console.log('3) 启动 UI 组件库')
    console.log('4) 启动 Flow 组件库')
    console.log('5) 启动所有应用')
    console.log('6) 停止所有应用')
    console.log('7) 显示状态')
    console.log('8) 创建新应用')
    console.log('9) 创建新包')
    console.log('10) 返回命令模式')
    console.log()
  }

  async handleMenuChoice(choice) {
    switch (choice) {
      case '1':
        await this.startApp('mattverse')
        break
      case '2':
        await this.startApp('highpower')
        break
      case '3':
        await this.startApp('ui')
        break
      case '4':
        await this.startApp('flow')
        break
      case '5':
        for (const appKey of Object.keys(APPS)) {
          await this.startApp(appKey)
          await new Promise((resolve) => setTimeout(resolve, 1000))
        }
        break
      case '6':
        this.stopAll()
        break
      case '7':
        this.showStatus()
        break
      case '8':
        await this.runGenerator('create-app')
        break
      case '9':
        await this.runGenerator('create-package')
        break
      case '10':
        return false // 返回命令模式
      default:
        this.log('无效选项，请输入 1-10', 'red')
    }
    return true // 继续菜单模式
  }

  async handleCommand(input) {
    const [command, ...args] = input.trim().split(' ')

    switch (command) {
      case 'start':
        if (args[0] && APPS[args[0]]) {
          await this.startApp(args[0])
        } else {
          this.log('请指定有效的应用名称', 'red')
          this.log('可用应用: ' + Object.keys(APPS).join(', '), 'yellow')
        }
        break

      case 'stop':
        if (args[0] && APPS[args[0]]) {
          this.stopApp(args[0])
        } else {
          this.log('请指定有效的应用名称', 'red')
          this.log('可用应用: ' + Object.keys(APPS).join(', '), 'yellow')
        }
        break

      case 'restart':
        if (args[0] && APPS[args[0]]) {
          this.stopApp(args[0])
          await new Promise((resolve) => setTimeout(resolve, 2000))
          await this.startApp(args[0])
        } else {
          this.log('请指定有效的应用名称', 'red')
          this.log('可用应用: ' + Object.keys(APPS).join(', '), 'yellow')
        }
        break

      case 'start-all':
        for (const appKey of Object.keys(APPS)) {
          await this.startApp(appKey)
          await new Promise((resolve) => setTimeout(resolve, 1000))
        }
        break

      case 'stop-all':
        this.stopAll()
        break

      case 'status':
        this.showStatus()
        break

      case 'help':
        this.showHelp()
        break

      case 'menu':
        await this.enterMenuMode()
        break

      case 'create-app':
        await this.runGenerator('create-app')
        break

      case 'create-package':
        await this.runGenerator('create-package')
        break

      case 'quit':
      case 'exit':
        this.stopAll()
        this.rl.close()
        process.exit(0)

      default:
        this.log('未知命令，输入 help 查看帮助', 'red')
    }
  }

  async runGenerator(type) {
    this.log(`启动${type === 'create-app' ? '应用' : '包'}生成器...`, 'cyan')
    this.log('生成器将在新的进程中运行', 'yellow')

    // 完全关闭当前的 readline 接口，避免冲突
    this.rl.close()

    const { spawn } = await import('child_process')
    const scriptPath = `scripts/generators/${type}.js`

    return new Promise((resolve) => {
      const child = spawn('node', [scriptPath], {
        stdio: 'inherit',
        shell: true,
        cwd: process.cwd(),
        detached: false
      })

      child.on('close', (code) => {
        // 重新创建 readline 接口
        this.rl = createInterface({
          input: process.stdin,
          output: process.stdout,
        })

        // 重新设置事件监听
        this.setupReadlineHandlers()

        if (code === 0) {
          this.log('生成器执行完成', 'green')
        } else {
          this.log(`生成器执行失败，退出码: ${code}`, 'red')
        }

        resolve(code)
      })

      child.on('error', (error) => {
        // 重新创建 readline 接口
        this.rl = createInterface({
          input: process.stdin,
          output: process.stdout,
        })

        this.setupReadlineHandlers()
        this.log(`生成器执行出错: ${error.message}`, 'red')
        resolve(1)
      })
    })
  }

  setupReadlineHandlers() {
    // 重新设置命令行交互
    this.rl.on('line', async (input) => {
      if (input.trim()) {
        await this.handleCommand(input)
      }
      this.rl.prompt()
    })

    this.rl.setPrompt(colors.cyan + 'dev-manager> ' + colors.reset)
    this.rl.prompt()
  }

  async enterMenuMode() {
    this.log('进入菜单模式...', 'cyan')

    while (true) {
      this.showMenu()

      const choice = await new Promise((resolve) => {
        this.rl.question('请选择操作 (1-10): ', resolve)
      })

      const continueMenu = await this.handleMenuChoice(choice.trim())
      if (!continueMenu) {
        this.log('返回命令模式', 'cyan')
        break
      }

      // 等待一下再显示下一个菜单
      await new Promise((resolve) => setTimeout(resolve, 1000))
    }
  }

  async start() {
    console.clear()
    this.log('MattVerse 开发环境管理器启动', 'green')
    this.showHelp()

    // 处理退出信号
    process.on('SIGINT', () => {
      this.log('\n收到退出信号，正在停止所有服务...', 'yellow')
      this.stopAll()
      setTimeout(() => process.exit(0), 3000)
    })

    // 设置命令行交互
    this.setupReadlineHandlers()
  }
}

// 启动管理器
const manager = new DevManager()
manager.start()

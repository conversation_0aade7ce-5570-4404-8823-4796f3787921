#!/bin/bash
# Batch Clean Script - Linux/macOS
# Usage: ./scripts/maintenance/clean-all.sh [type]
# Type options: build, deps, cache, all (default: build)
# Interactive mode: Run without parameters for interactive selection

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Interactive cleanup type selection
select_cleanup_type() {
    echo -e "${CYAN}Select Cleanup Type:${NC}"
    echo "1) Build files (dist, out, release)"
    echo "2) Dependencies (node_modules, pnpm-lock.yaml)"
    echo "3) Cache files (.turbo, .eslintcache)"
    echo "4) All files (build + deps + cache)"
    echo "5) Exit"
    
    while true; do
        read -p "Please enter option (1-5): " choice
        case $choice in
            1) echo "build"; return ;;
            2) echo "deps"; return ;;
            3) echo "cache"; return ;;
            4) echo "all"; return ;;
            5) echo "exit"; return ;;
            *) echo -e "${RED}Invalid option, please enter 1-5${NC}" ;;
        esac
    done
}

# Confirm cleanup operation
confirm_cleanup() {
    local cleanup_type=$1
    
    echo -e "${YELLOW}Cleanup Configuration:${NC}"
    echo -e "  Type: ${GREEN}$cleanup_type${NC}"
    
    case $cleanup_type in
        "build") echo -e "  Will clean: ${BLUE}dist, out, release directories${NC}" ;;
        "deps") echo -e "  Will clean: ${BLUE}node_modules, pnpm-lock.yaml${NC}" ;;
        "cache") echo -e "  Will clean: ${BLUE}.turbo, .eslintcache, node_modules/.cache${NC}" ;;
        "all") echo -e "  Will clean: ${BLUE}ALL files (build + deps + cache)${NC}" ;;
    esac
    
    while true; do
        read -p $'\nConfirm cleanup? (y/n): ' confirm
        case $confirm in
            [Yy]* ) return 0 ;;
            [Nn]* ) return 1 ;;
            * ) echo -e "${RED}Please enter y or n${NC}" ;;
        esac
    done
}

# Clean build files
clean_build_files() {
    echo -e "${YELLOW}Cleaning build files...${NC}"
    
    # Clean application build files
    apps=("mattverse" "highpower")
    for app in "${apps[@]}"; do
        app_path="apps/$app"
        if [ -d "$app_path" ]; then
            echo -e "  Cleaning ${GREEN}$app${NC} build files..."
            
            # Clean dist directory
            if [ -d "$app_path/dist" ]; then
                rm -rf "$app_path/dist"
                echo -e "    ${GREEN}✅ Deleted $app_path/dist${NC}"
            fi
            
            # Clean out directory
            if [ -d "$app_path/out" ]; then
                rm -rf "$app_path/out"
                echo -e "    ${GREEN}✅ Deleted $app_path/out${NC}"
            fi
            
            # Clean release directory
            if [ -d "$app_path/release" ]; then
                rm -rf "$app_path/release"
                echo -e "    ${GREEN}✅ Deleted $app_path/release${NC}"
            fi
        fi
    done
    
    # Clean package build files
    packages=("configs" "shared" "electron-core" "mattverse-ui" "mattverse-flow")
    for pkg in "${packages[@]}"; do
        pkg_path="packages/$pkg"
        if [ -d "$pkg_path" ]; then
            echo -e "  Cleaning ${GREEN}$pkg${NC} build files..."
            
            if [ -d "$pkg_path/dist" ]; then
                rm -rf "$pkg_path/dist"
                echo -e "    ${GREEN}✅ Deleted $pkg_path/dist${NC}"
            fi
        fi
    done
}

# Clean dependencies
clean_dependencies() {
    echo -e "${YELLOW}Cleaning dependency files...${NC}"
    
    # Clean root node_modules
    if [ -d "node_modules" ]; then
        rm -rf "node_modules"
        echo -e "  ${GREEN}✅ Deleted root node_modules${NC}"
    fi
    
    # Clean pnpm-lock.yaml
    if [ -f "pnpm-lock.yaml" ]; then
        rm -f "pnpm-lock.yaml"
        echo -e "  ${GREEN}✅ Deleted pnpm-lock.yaml${NC}"
    fi
    
    # Clean app node_modules
    apps=("mattverse" "highpower")
    for app in "${apps[@]}"; do
        node_modules_path="apps/$app/node_modules"
        if [ -d "$node_modules_path" ]; then
            rm -rf "$node_modules_path"
            echo -e "  ${GREEN}✅ Deleted $app node_modules${NC}"
        fi
    done
    
    # Clean package node_modules
    packages=("configs" "shared" "electron-core" "mattverse-ui" "mattverse-flow")
    for pkg in "${packages[@]}"; do
        node_modules_path="packages/$pkg/node_modules"
        if [ -d "$node_modules_path" ]; then
            rm -rf "$node_modules_path"
            echo -e "  ${GREEN}✅ Deleted $pkg node_modules${NC}"
        fi
    done
}

# Clean cache files
clean_cache() {
    echo -e "${YELLOW}Cleaning cache files...${NC}"
    
    # Clean Turbo cache
    if [ -d ".turbo" ]; then
        rm -rf ".turbo"
        echo -e "  ${GREEN}✅ Deleted Turbo cache${NC}"
    fi
    
    # Clean node_modules cache
    if [ -d "node_modules/.cache" ]; then
        rm -rf "node_modules/.cache"
        echo -e "  ${GREEN}✅ Deleted node_modules cache${NC}"
    fi
    
    # Clean ESLint cache
    if [ -f ".eslintcache" ]; then
        rm -f ".eslintcache"
        echo -e "  ${GREEN}✅ Deleted ESLint cache${NC}"
    fi
}

# Check if running in project root
if [ ! -f "package.json" ]; then
    echo -e "${RED}Please run this script from project root directory${NC}"
    exit 1
fi

# Get cleanup type
TYPE=${1:-""}

# Check for interactive mode
if [ -z "$TYPE" ]; then
    echo -e "${BLUE}MattVerse Interactive Cleanup Tool${NC}"
    echo "=================================="
    
    while true; do
        # Select cleanup type
        TYPE=$(select_cleanup_type)
        if [ "$TYPE" = "exit" ]; then
            echo -e "${YELLOW}Cleanup cancelled${NC}"
            exit 0
        fi
        
        # Confirm operation
        if confirm_cleanup "$TYPE"; then
            break
        else
            echo -e "${YELLOW}Reconfiguring...${NC}"
            echo
        fi
    done
fi

echo -e "${GREEN}Starting project cleanup...${NC}"
echo -e "${CYAN}Cleanup type: ${GREEN}$TYPE${NC}"

# Execute cleanup based on type
case $TYPE in
    "build")
        clean_build_files
        ;;
    "deps")
        clean_dependencies
        ;;
    "cache")
        clean_cache
        ;;
    "all")
        clean_build_files
        clean_cache
        clean_dependencies
        ;;
    *)
        echo -e "${RED}Unsupported cleanup type: $TYPE${NC}"
        echo -e "${YELLOW}Supported types: build, deps, cache, all${NC}"
        exit 1
        ;;
esac

echo -e "${GREEN}Cleanup completed!${NC}"

if [ "$TYPE" = "deps" ] || [ "$TYPE" = "all" ]; then
    echo -e "${YELLOW}Suggestion: Run 'pnpm install' to reinstall dependencies${NC}"
fi

#!/usr/bin/env node
/**
 * 项目健康检查脚本
 * 检查项目配置、依赖、构建状态等
 */

import { existsSync, readFileSync, statSync } from 'fs'
import { join } from 'path'
import { spawn } from 'child_process'

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green')
}

function logError(message) {
  log(`❌ ${message}`, 'red')
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow')
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue')
}

class HealthChecker {
  constructor() {
    this.issues = []
    this.warnings = []
    this.rootPath = process.cwd()
  }

  addIssue(message) {
    this.issues.push(message)
    logError(message)
  }

  addWarning(message) {
    this.warnings.push(message)
    logWarning(message)
  }

  checkFileExists(filePath, description) {
    const fullPath = join(this.rootPath, filePath)
    if (existsSync(fullPath)) {
      logSuccess(`${description} 存在`)
      return true
    } else {
      this.addIssue(`${description} 不存在: ${filePath}`)
      return false
    }
  }

  checkPackageJson() {
    log('\n📦 检查 package.json 配置...', 'cyan')
    
    const packageJsonPath = join(this.rootPath, 'package.json')
    if (!this.checkFileExists('package.json', 'Root package.json')) {
      return
    }

    try {
      const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'))
      
      // 检查必要字段
      const requiredFields = ['name', 'version', 'scripts', 'workspaces']
      requiredFields.forEach(field => {
        if (packageJson[field]) {
          logSuccess(`package.json 包含 ${field} 字段`)
        } else {
          this.addIssue(`package.json 缺少 ${field} 字段`)
        }
      })

      // 检查工作空间配置
      if (packageJson.workspaces) {
        const expectedWorkspaces = ['apps/*', 'packages/*']
        expectedWorkspaces.forEach(workspace => {
          if (packageJson.workspaces.includes(workspace)) {
            logSuccess(`工作空间包含 ${workspace}`)
          } else {
            this.addWarning(`工作空间缺少 ${workspace}`)
          }
        })
      }

      // 检查关键脚本
      const requiredScripts = ['build', 'dev', 'lint', 'typecheck']
      requiredScripts.forEach(script => {
        if (packageJson.scripts && packageJson.scripts[script]) {
          logSuccess(`脚本 ${script} 已配置`)
        } else {
          this.addIssue(`缺少脚本: ${script}`)
        }
      })

    } catch (error) {
      this.addIssue(`package.json 解析失败: ${error.message}`)
    }
  }

  checkWorkspaceStructure() {
    log('\n🏗️  检查工作空间结构...', 'cyan')
    
    const expectedDirs = [
      'apps',
      'packages',
      'scripts',
      'docs'
    ]

    expectedDirs.forEach(dir => {
      this.checkFileExists(dir, `目录 ${dir}`)
    })

    // 检查应用目录
    const apps = ['mattverse', 'highpower']
    apps.forEach(app => {
      const appPath = `apps/${app}`
      if (this.checkFileExists(appPath, `应用 ${app}`)) {
        this.checkFileExists(`${appPath}/package.json`, `${app} package.json`)
        this.checkFileExists(`${appPath}/src`, `${app} src 目录`)
      }
    })

    // 检查包目录
    const packages = ['configs', 'shared', 'electron-core', 'mattverse-ui', 'mattverse-flow']
    packages.forEach(pkg => {
      const pkgPath = `packages/${pkg}`
      if (this.checkFileExists(pkgPath, `包 ${pkg}`)) {
        this.checkFileExists(`${pkgPath}/package.json`, `${pkg} package.json`)
        this.checkFileExists(`${pkgPath}/src`, `${pkg} src 目录`)
      }
    })
  }

  checkConfigFiles() {
    log('\n⚙️  检查配置文件...', 'cyan')
    
    const configFiles = [
      { path: 'turbo.json', desc: 'Turbo 配置' },
      { path: 'pnpm-workspace.yaml', desc: 'pnpm 工作空间配置' },
      { path: 'tsconfig.json', desc: 'TypeScript 根配置' },
      { path: '.gitignore', desc: 'Git 忽略文件' },
      { path: 'prettier.config.js', desc: 'Prettier 配置' },
      { path: 'eslint.config.js', desc: 'ESLint 配置' }
    ]

    configFiles.forEach(({ path, desc }) => {
      this.checkFileExists(path, desc)
    })
  }

  async checkDependencies() {
    log('\n📚 检查依赖状态...', 'cyan')
    
    return new Promise((resolve) => {
      const child = spawn('pnpm', ['list', '--depth=0'], {
        stdio: 'pipe',
        shell: true,
        cwd: this.rootPath
      })

      let output = ''
      child.stdout.on('data', (data) => {
        output += data.toString()
      })

      child.on('close', (code) => {
        if (code === 0) {
          logSuccess('依赖检查通过')
        } else {
          this.addWarning('依赖可能存在问题，建议运行 pnpm install')
        }
        resolve()
      })

      child.on('error', () => {
        this.addWarning('无法检查依赖状态')
        resolve()
      })
    })
  }

  checkBuildArtifacts() {
    log('\n🔨 检查构建产物...', 'cyan')
    
    const packages = ['configs', 'shared', 'electron-core', 'mattverse-ui', 'mattverse-flow']
    packages.forEach(pkg => {
      const distPath = `packages/${pkg}/dist`
      if (existsSync(join(this.rootPath, distPath))) {
        logSuccess(`${pkg} 构建产物存在`)
      } else {
        this.addWarning(`${pkg} 构建产物不存在，可能需要运行构建`)
      }
    })
  }

  checkGitStatus() {
    log('\n📝 检查 Git 状态...', 'cyan')
    
    return new Promise((resolve) => {
      const child = spawn('git', ['status', '--porcelain'], {
        stdio: 'pipe',
        shell: true,
        cwd: this.rootPath
      })

      let output = ''
      child.stdout.on('data', (data) => {
        output += data.toString()
      })

      child.on('close', (code) => {
        if (code === 0) {
          const changes = output.trim().split('\n').filter(line => line.trim())
          if (changes.length === 0) {
            logSuccess('工作目录干净')
          } else {
            this.addWarning(`有 ${changes.length} 个未提交的更改`)
          }
        } else {
          this.addWarning('无法检查 Git 状态')
        }
        resolve()
      })

      child.on('error', () => {
        this.addWarning('Git 不可用')
        resolve()
      })
    })
  }

  generateReport() {
    log('\n📊 健康检查报告', 'cyan')
    log('='.repeat(50), 'cyan')
    
    if (this.issues.length === 0 && this.warnings.length === 0) {
      logSuccess('项目状态良好！')
    } else {
      if (this.issues.length > 0) {
        log(`\n❌ 发现 ${this.issues.length} 个问题:`, 'red')
        this.issues.forEach((issue, index) => {
          log(`  ${index + 1}. ${issue}`, 'red')
        })
      }

      if (this.warnings.length > 0) {
        log(`\n⚠️  发现 ${this.warnings.length} 个警告:`, 'yellow')
        this.warnings.forEach((warning, index) => {
          log(`  ${index + 1}. ${warning}`, 'yellow')
        })
      }

      log('\n💡 建议操作:', 'blue')
      if (this.issues.some(issue => issue.includes('依赖'))) {
        log('  - 运行 pnpm install 安装依赖', 'blue')
      }
      if (this.warnings.some(warning => warning.includes('构建产物'))) {
        log('  - 运行 pnpm build 构建项目', 'blue')
      }
      if (this.issues.some(issue => issue.includes('配置'))) {
        log('  - 检查并修复配置文件', 'blue')
      }
    }

    log('\n🎯 快速修复命令:', 'cyan')
    log('  pnpm install     # 安装依赖', 'cyan')
    log('  pnpm build       # 构建项目', 'cyan')
    log('  pnpm lint:fix    # 修复代码风格', 'cyan')
    log('  pnpm clean:all   # 清理缓存', 'cyan')
  }

  async run() {
    log('🔍 开始项目健康检查...', 'bright')
    
    this.checkPackageJson()
    this.checkWorkspaceStructure()
    this.checkConfigFiles()
    await this.checkDependencies()
    this.checkBuildArtifacts()
    await this.checkGitStatus()
    
    this.generateReport()
    
    // 返回状态码
    return this.issues.length === 0 ? 0 : 1
  }
}

// 运行健康检查
const checker = new HealthChecker()
checker.run().then(exitCode => {
  process.exit(exitCode)
}).catch(error => {
  logError(`健康检查失败: ${error.message}`)
  process.exit(1)
})

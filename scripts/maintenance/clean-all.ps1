# Batch Clean Script - Windows
# Usage: .\scripts\maintenance\clean-all.ps1 [type]
# Type options: build, deps, cache, all (default: build)
# Interactive mode: Run without parameters for interactive selection

param(
    [string]$Type = ""
)

# Interactive cleanup type selection
function Select-CleanupType {
    Write-Host ""
    Write-Host "Select Cleanup Type:" -ForegroundColor Cyan
    Write-Host "1) Build files (dist, out, release)" -ForegroundColor Green
    Write-Host "2) Dependencies (node_modules, pnpm-lock.yaml)" -ForegroundColor Green
    Write-Host "3) Cache files (.turbo, .eslintcache)" -ForegroundColor Green
    Write-Host "4) All files (build + deps + cache)" -ForegroundColor Green
    Write-Host "5) Exit" -ForegroundColor Red

    do {
        $choice = Read-Host "Please enter option (1-5)"
        switch ($choice) {
            "1" { return "build" }
            "2" { return "deps" }
            "3" { return "cache" }
            "4" { return "all" }
            "5" { return "exit" }
            default { Write-Host "Invalid option, please enter 1-5" -ForegroundColor Red }
        }
    } while ($true)
}

# Confirm cleanup operation
function Confirm-Cleanup {
    param([string]$CleanupType)

    Write-Host ""
    Write-Host "Cleanup Configuration:" -ForegroundColor Yellow
    Write-Host "  Type: $CleanupType" -ForegroundColor Green

    switch ($CleanupType) {
        "build" { Write-Host "  Will clean: dist, out, release directories" -ForegroundColor Blue }
        "deps" { Write-Host "  Will clean: node_modules, pnpm-lock.yaml" -ForegroundColor Blue }
        "cache" { Write-Host "  Will clean: .turbo, .eslintcache, node_modules\.cache" -ForegroundColor Blue }
        "all" { Write-Host "  Will clean: ALL files (build + deps + cache)" -ForegroundColor Blue }
    }

    do {
        $confirm = Read-Host "`nConfirm cleanup? (y/n)"
        switch ($confirm.ToLower()) {
            "y" { return $true }
            "yes" { return $true }
            "n" { return $false }
            "no" { return $false }
            default { Write-Host "Please enter y or n" -ForegroundColor Red }
        }
    } while ($true)
}

$ErrorActionPreference = "Stop"

# Check for interactive mode
if ([string]::IsNullOrEmpty($Type)) {
    Write-Host "MattVerse Interactive Cleanup Tool" -ForegroundColor Blue
    Write-Host "==================================" -ForegroundColor Cyan

    do {
        # Select cleanup type
        $Type = Select-CleanupType
        if ($Type -eq "exit") {
            Write-Host "Cleanup cancelled" -ForegroundColor Yellow
            exit 0
        }

        # Confirm operation
        if (Confirm-Cleanup -CleanupType $Type) {
            break
        } else {
            Write-Host "Reconfiguring..." -ForegroundColor Yellow
        }
    } while ($true)
} else {
    # Command line mode - set default if empty
    if ([string]::IsNullOrEmpty($Type)) {
        $Type = "build"
    }
}

Write-Host "Starting project cleanup..." -ForegroundColor Green

function Clean-BuildFiles {
    Write-Host "Cleaning build files..." -ForegroundColor Yellow

    # Clean application build files
    $apps = @("mattverse", "highpower")
    foreach ($app in $apps) {
        $appPath = "apps\$app"
        if (Test-Path $appPath) {
            Write-Host "  Cleaning $app build files..." -ForegroundColor Cyan

            # Clean dist directory
            $distPath = "$appPath\dist"
            if (Test-Path $distPath) {
                Remove-Item $distPath -Recurse -Force
                Write-Host "    Deleted $distPath" -ForegroundColor Green
            }

            # Clean out directory
            $outPath = "$appPath\out"
            if (Test-Path $outPath) {
                Remove-Item $outPath -Recurse -Force
                Write-Host "    Deleted $outPath" -ForegroundColor Green
            }

            # Clean release directory
            $releasePath = "$appPath\release"
            if (Test-Path $releasePath) {
                Remove-Item $releasePath -Recurse -Force
                Write-Host "    Deleted $releasePath" -ForegroundColor Green
            }
        }
    }

    # Clean package build files
    $packages = @("configs", "shared", "electron-core", "mattverse-ui", "mattverse-flow")
    foreach ($pkg in $packages) {
        $pkgPath = "packages\$pkg"
        if (Test-Path $pkgPath) {
            Write-Host "  Cleaning $pkg build files..." -ForegroundColor Cyan

            $distPath = "$pkgPath\dist"
            if (Test-Path $distPath) {
                Remove-Item $distPath -Recurse -Force
                Write-Host "    Deleted $distPath" -ForegroundColor Green
            }
        }
    }
}

function Clean-Dependencies {
    Write-Host "Cleaning dependency files..." -ForegroundColor Yellow

    # Clean root node_modules
    if (Test-Path "node_modules") {
        Remove-Item "node_modules" -Recurse -Force
        Write-Host "  Deleted root node_modules" -ForegroundColor Green
    }

    # Clean pnpm-lock.yaml
    if (Test-Path "pnpm-lock.yaml") {
        Remove-Item "pnpm-lock.yaml" -Force
        Write-Host "  Deleted pnpm-lock.yaml" -ForegroundColor Green
    }

    # Clean app node_modules
    $apps = @("mattverse", "highpower")
    foreach ($app in $apps) {
        $nodeModulesPath = "apps\$app\node_modules"
        if (Test-Path $nodeModulesPath) {
            Remove-Item $nodeModulesPath -Recurse -Force
            Write-Host "  Deleted $app node_modules" -ForegroundColor Green
        }
    }

    # Clean package node_modules
    $packages = @("configs", "shared", "electron-core", "mattverse-ui", "mattverse-flow")
    foreach ($pkg in $packages) {
        $nodeModulesPath = "packages\$pkg\node_modules"
        if (Test-Path $nodeModulesPath) {
            Remove-Item $nodeModulesPath -Recurse -Force
            Write-Host "  Deleted $pkg node_modules" -ForegroundColor Green
        }
    }
}

function Clean-Cache {
    Write-Host "Cleaning cache files..." -ForegroundColor Yellow

    # Clean Turbo cache
    if (Test-Path ".turbo") {
        Remove-Item ".turbo" -Recurse -Force
        Write-Host "  Deleted Turbo cache" -ForegroundColor Green
    }

    # Clean node_modules cache
    $cacheDir = "node_modules\.cache"
    if (Test-Path $cacheDir) {
        Remove-Item $cacheDir -Recurse -Force
        Write-Host "  Deleted node_modules cache" -ForegroundColor Green
    }

    # Clean ESLint cache
    if (Test-Path ".eslintcache") {
        Remove-Item ".eslintcache" -Force
        Write-Host "  Deleted ESLint cache" -ForegroundColor Green
    }
}

# Check if running in project root
if (-not (Test-Path "package.json")) {
    Write-Host "Please run this script from project root directory" -ForegroundColor Red
    exit 1
}

try {
    Write-Host "Cleanup type: $Type" -ForegroundColor Cyan

    switch ($Type.ToLower()) {
        "build" {
            Clean-BuildFiles
        }
        "deps" {
            Clean-Dependencies
        }
        "cache" {
            Clean-Cache
        }
        "all" {
            Clean-BuildFiles
            Clean-Cache
            Clean-Dependencies
        }
        default {
            Write-Host "Unsupported cleanup type: $Type" -ForegroundColor Red
            Write-Host "Supported types: build, deps, cache, all" -ForegroundColor Yellow
            exit 1
        }
    }

    Write-Host "Cleanup completed!" -ForegroundColor Green

    if ($Type -eq "deps" -or $Type -eq "all") {
        Write-Host "Suggestion: Run 'pnpm install' to reinstall dependencies" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "Error during cleanup: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
